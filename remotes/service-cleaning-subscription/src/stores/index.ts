import {
  IAddons,
  IAddress,
  IDate,
  IPriceSub,
  IService,
  ISO_CODE,
  Maybe,
  Requirement,
} from '@btaskee/design-system';
import { create } from 'zustand';

interface AppState {
  address: IAddress;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date: IDate | null;
  schedule: string[];
  isEnabledSchedule: boolean;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: IPriceSub | null;
  service: Maybe<IService> | null;
  paymentMethod: any;
  promotion: any;
  isLoadingPrice: boolean;
  loadingPostTask: boolean;
  homeNumber: string;
  isEco: boolean;
  month: number;
  startDate: IDate | null;
  endDate: IDate | null;
  weekdays: number[];
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date: IDate) => void;
  setSchedule: (schedule?: string[]) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price: IPriceSub | null) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (isLoadingPrice: boolean) => void;
  setService: (service: Maybe<IService>) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setMonth: (month: number) => void;
  setStartDate: (startDate: IDate) => void;
  setEndDate: (endDate: IDate) => void;
  setWeekdays: (weekdays?: number[]) => void;
  setIsEco: (isEco: boolean) => void;
  resetState: () => void;
}

export const usePostTaskStore = create<AppState>((set) => ({
  address: {},
  duration: 0,
  requirements: [],
  isPremium: false,
  isAutoChooseTasker: true,
  isFavouriteTasker: false,
  gender: '',
  pet: '',
  addons: [],
  date: null,
  schedule: [],
  isEnabledSchedule: false,
  note: '',
  isApplyNoteForAllTask: false,
  dateOptions: [],
  homeNumber: '',
  price: null,
  service: null,
  timezone: 'Asia/Ho_Chi_Minh',
  forceTasker: {},
  isoCode: ISO_CODE.VN,
  dataQuickPostTask: null,
  currency: null,
  paymentMethod: {
    name: 'Cash',
    label: 'PAYMENT_METHOD_DIRECT_CASH',
    value: 'CASH',
    icon: { uri: 'https://i.ibb.co/v4C7nhW4/logo-cash.png' },
  },
  promotion: null,
  isLoadingPrice: false,
  loadingPostTask: false,
  relatedTask: null,
  isEco: false,
  month: 0,
  startDate: null,
  endDate: null,
  weekdays: [],
  setLoadingPostTask: (loadingPostTask: boolean) =>
    set({ loadingPostTask: loadingPostTask }),
  setAddress: (address: IAddress) => set({ address: address }),
  setDuration: (duration: number) => set({ duration: duration }),
  setRequirements: (requirements: Requirement[]) =>
    set({ requirements: requirements }),
  setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
    set({ isAutoChooseTasker: isAutoChooseTasker }),
  setGender: (gender: string) => set({ gender: gender }),
  setAddons: (addons: IAddons[]) => set({ addons: addons }),
  setPet: (pet: any) => set({ pet: pet }),
  setDateTime: (date: IDate) => set({ date: date }),
  setSchedule: (schedule?: string[]) => set({ schedule: schedule }),
  setNote: (note: string) => set({ note: note }),
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
    set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
  setPrice: (price: IPriceSub | null) => set({ price: price }),
  setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
  setPaymentMethod: (paymentMethod: any) =>
    set({ paymentMethod: paymentMethod }),
  setPromotion: (promotion: any) => set({ promotion: promotion }),
  setLoadingPrice: (isLoadingPrice: boolean) =>
    set({ isLoadingPrice: isLoadingPrice }),
  setService: (service: Maybe<IService>) => set({ service: service }),
  setMonth: (month: number) => set({ month: month }),
  setStartDate: (startDate: IDate) => set({ startDate: startDate }),
  setEndDate: (endDate: IDate) => set({ endDate: endDate }),
  setWeekdays: (weekdays?: number[]) => set({ weekdays: weekdays }),
  setIsEco: (isEco: boolean) => set({ isEco: isEco }),
  resetState: () =>
    set({
      address: {},
      duration: 0,
      requirements: [],
      isPremium: false,
      isAutoChooseTasker: true,
      isFavouriteTasker: false,
      gender: '',
      pet: '',
      addons: [],
      date: null,
      schedule: [],
      isEnabledSchedule: false,
      note: '',
      isApplyNoteForAllTask: false,
      homeNumber: '',
      price: null,
      service: null,
      paymentMethod: {
        name: 'Cash',
        label: 'PAYMENT_METHOD_DIRECT_CASH',
        value: 'CASH',
        icon: { uri: 'https://i.ibb.co/v4C7nhW4/logo-cash.png' },
      },
      promotion: null,
      isLoadingPrice: false,
      loadingPostTask: false,
      isEco: false,
      month: 0,
      startDate: null,
      endDate: null,
      weekdays: [],
    }),
}));
