import '../i18n';

import React, { useEffect } from 'react';
import {
  BlockView,
  Colors,
  CText,
  FontFamily,
  getDefaultPaymentMethod,
  IconAssets,
  IconImage,
  IService,
  SERVICES,
  Spacing,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import {
  ChooseAddress,
  ChooseService,
  ConfirmAndPayment,
  NotesForTasker,
  PostTaskSuccess,
} from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';
import { MainStackParamList } from './type';

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();

  const { address, setService, setPaymentMethod } = usePostTaskStore();
  const { settings } = useSettingsStore();

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    const cleaningSubscriptionService = settings?.services?.find(
      (service: IService) => service?.name === SERVICES.CLEANING_SUBSCRIPTION,
    );
    setService(cleaningSubscriptionService);
    setPaymentMethod(
      getDefaultPaymentMethod({
        serviceName: SERVICES.CLEANING_SUBSCRIPTION,
      }),
    );
  };

  const renderTitle = () => {
    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={Colors.RED}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText>{address?.shortAddress}</CText>
          <CText
            bold
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
          >
            {address?.address}
          </CText>
        </BlockView>
      </BlockView>
    );
  };

  const renderHeaderLeft = (navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}
      >
        <IconImage
          source={IconAssets.icBack}
          size={24}
          color={Colors.BLACK}
        />
      </TouchableOpacity>
    );
  };

  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft(navigation),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: Colors.WHITE },
        headerStyle: {
          backgroundColor: Colors.WHITE,
        },
        headerTitleStyle: {
          color: Colors.BLACK,
          fontSize: 18,
          fontFamily: FontFamily.bold,
        },
      })}
      initialRouteName={RouteName.ChooseAddress}
    >
      <Stack.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress}
        options={{ title: t('LIST_OF_LOCATIONS') }}
      />
      <Stack.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
        options={{ headerTitle: renderTitle }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.NotesForTasker}
        component={NotesForTasker}
        options={{ title: t('POST_TASK_NOTE') }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
