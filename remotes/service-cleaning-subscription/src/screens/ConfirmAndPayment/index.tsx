import React from 'react';
import {
  BlockView,
  BookingButton,
  ColorsV2,
  LocationPostTask,
  PaymentDetail,
  PaymentMethod,
  RefundNote,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';

import { TaskDetail } from '@components';
import { useAppNavigation, usePostTask } from '@hooks';
import { usePostTaskStore } from '@stores';

import styles from './styles';

export const ConfirmAndPayment = () => {
  const navigation = useAppNavigation();
  const {
    address,
    homeNumber,
    setAddress,
    price,
    setPromotion,
    paymentMethod,
    promotion,
    setPaymentMethod,
  } = usePostTaskStore();
  const { postTask } = usePostTask();
  return (
    <BlockView
      flex
      inset={'bottom'}
      backgroundColor={ColorsV2.neutralBackground}
      padding={{ horizontal: Spacing.SPACE_16 }}
    >
      <ScrollView
        testID="scrollStep4"
        scrollIndicatorInsets={{ right: 1 }}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <LocationPostTask
          address={address}
          homeNumber={homeNumber}
          setAddress={setAddress}
        />

        <TaskDetail />

        <PaymentDetail price={price} />

        <PaymentMethod
          setPaymentMethod={setPaymentMethod}
          paymentMethod={paymentMethod}
          promotion={promotion}
          setPromotion={setPromotion}
        />

        <RefundNote />
      </ScrollView>
      <BookingButton
        navigation={navigation}
        price={price}
        onPostTask={postTask}
      />
    </BlockView>
  );
};
