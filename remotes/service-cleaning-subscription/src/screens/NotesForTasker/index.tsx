import React, { useCallback, useMemo, useState } from 'react';
import { Keyboard, TouchableWithoutFeedback } from 'react-native';
import {
  BlockView,
  CText,
  CTextInput,
  PrimaryButton,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const NotesForTasker = () => {
  const { t } = useI18n();

  const navigation = useAppNavigation();
  const { setNote: setNotePostTask } = usePostTaskStore();

  const [note, setNote] = useState('');
  const [error, setError] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const isDisabled = useMemo(() => {
    return !note || note?.trim()?.length < 10;
  }, [note]);

  const onSubmit = useCallback(async () => {
    // Submit for subscription
    navigation.navigate(RouteName.ConfirmAndPayment);
    setNotePostTask(note);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [note, navigation]);

  const onChangeText = useCallback((value: string) => {
    setNote(value);
    setError(''); // Clear error when user types
  }, []);

  const handleFocus = useCallback(() => {
    setIsTyping(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsTyping(false);
  }, []);

  const button = useMemo(() => {
    return (
      <PrimaryButton
        onPress={onSubmit}
        title={t('CONTINUE')}
        disabled={isDisabled}
      />
    );
  }, [isDisabled, onSubmit, t]);

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <BlockView
        inset={'bottom'}
        style={styles.container}
      >
        <BlockView flex>
          <CTextInput
            inputStyle={styles.inputStyle}
            placeholder={t('POST_TASK_NOTE')}
            containerStyle={styles.inputContainer}
            multiline
            labelStyle={styles.labelStyle}
            maxLength={400}
            onChangeText={onChangeText}
            value={note}
            error={error}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
          <CText
            testID="taskNoteDescription"
            style={styles.txtDescription}
          >
            {t('TASK_NOTE_DESCRIPTION')}
          </CText>
        </BlockView>

        {!isTyping && button}
      </BlockView>
    </TouchableWithoutFeedback>
  );
};
