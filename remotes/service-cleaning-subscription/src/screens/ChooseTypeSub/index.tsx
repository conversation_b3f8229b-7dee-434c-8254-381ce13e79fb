import React, { useMemo } from 'react';
import {} from 'react-native';
import {
  BlockView,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  IconAssets,
  IconImage,
  ScrollView,
  TouchableOpacity,
} from '@btaskee/design-system';

import { WorkingTime } from '@components';
import { useAppNavigation, useI18n } from '@hooks';
import { imgBgHeader, imgEcoSub, imgStandardSub } from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

export const ChooseTypeSub = ({ renewOldSubscription }: any) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();

  const optionToolBar = useMemo(() => {
    return {
      headerTitle: (
        <CText
          bold
          size={FontSizes.SIZE_20}
          color={ColorsV2.neutralWhite}
        >
          {t('CURTAIN_CHOOSE_SERVICE')}
        </CText>
      ),
      headerLeft: (
        <TouchableOpacity
          onPress={() => navigation?.goBack?.()}
          activeOpacity={0.7}
        >
          <IconImage
            source={IconAssets.icBack}
            size={24}
            color={ColorsV2.neutralWhite}
          />
        </TouchableOpacity>
      ),
    };
  }, [navigation, t]);

  React.useLayoutEffect(() => {
    const { headerTitle, headerLeft } = optionToolBar;

    navigation.setOptions({
      headerTransparent: true,
      headerStyle: {
        height: 100,
      },
      headerTintColor: ColorsV2.neutralWhite,
      headerTitle: () => headerTitle,
      headerLeft: () => headerLeft,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  const _onChoosePostTaskNormal = async () => {
    // if (!isEmpty(subscriptionData)) {
    // return navigation.replace(RouteName.SubscriptionUpdate, {
    //   subscriptionData: subscriptionData,
    //   isShowReviewSchedule,
    //   isShowEcoOption: true,
    // });
    // }
    navigation.navigate(RouteName.ChooseDuration, {
      isShowEcoOption: true,
      renewOldSubscription,
    });
  };

  const _onChoosePostSubscription = async () => {
    // if (!isEmpty(subscriptionData)) {
    // return navigation.replace(RouteName.SubscriptionUpdate, {
    //   subscriptionData: subscriptionData,
    //   isShowReviewSchedule,
    // });
    // }
    navigation.navigate(RouteName.ChooseDuration, { renewOldSubscription });
  };

  return (
    <BlockView style={styles.container}>
      <FastImage
        resizeMode="cover"
        source={imgBgHeader}
        style={styles.imageHeader}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
      >
        <BlockView style={styles.content}>
          <WorkingTime
            testID={'btnChooseEcoSub'}
            image={imgEcoSub}
            onPress={_onChoosePostTaskNormal}
            title={t('SUBSCRIPTION.ECO_SUBSCRIPTION')}
            contents={[t('SUBSCRIPTION.ECO_SUBSCRIPTION_CONTENT')]}
          />

          <WorkingTime
            image={imgStandardSub}
            testID={'chooseStandardSub'}
            onPress={_onChoosePostSubscription}
            title={t('SUBSCRIPTION.NORMAL_SUBSCRIPTION')}
            contents={[t('SUBSCRIPTION.NORMAL_SUBSCRIPTION_CONTENT')]}
          />
        </BlockView>
      </ScrollView>
    </BlockView>
  );
};
