/**
 * ChooseAddress Screen
 *
 * Displays a list of locations for the user to choose from when booking a cleaning service.
 * Allows users to select an address which will be used for the cleaning service.
 */
import React, { memo, useCallback, useEffect, useRef } from 'react';
import { Animated, ListRenderItem } from 'react-native';
import {
  AnimationHelpers,
  BlockView,
  CText,
  FlatList,
  IAddress,
  LocationItem,
  SizedBox,
  useUserStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { NavigationProps } from '@navigation/type';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

// Mock data for locations - in a real app, this would likely come from an API or store
const LOCATION_DATA: IAddress[] = [
  {
    _id: 'xd6a36168ada192611c922cc962907a04',
    address: 'Quận 1, Hồ Chí <PERSON>, Việt Nam',
    city: '<PERSON><PERSON>',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'uỷ ban',
    district: 'Quận 1',
    homeType: 'HOME_OFFICE',
    isoCode: 'VN',
    lat: 10.7756587,
    lng: 106.7004238,
    phoneNumber: '0777777777',
    shortAddress: 'District 1 Ho Chi Minh City',
  },
  {
    _id: 'x02d858e8bb2c0e01de8216b7a6a3d801',
    address: 'Thanh Hóa, Việt Nam',
    city: 'Thanh Hóa',
    contact: 'Quan test',
    country: 'VN',
    countryCode: '+84',
    description: 'ma',
    district: 'Thành phố Thanh Hoá',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 19.806692,
    lng: 105.7851816,
    phoneNumber: '0397411511',
    shortAddress: 'thanh hoá',
  },
  {
    _id: 'xc3f74b0de1f15cfcb59df62a8ad60791',
    address:
      'Place in Saigon apartment 2, Phường 22, Bình Thạnh, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: '222',
    district: 'Bình Thạnh',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.7906155,
    lng: 106.7173618,
    phoneNumber: '0777777777',
    shortAddress: 'Vietnam Bình Thạnh',
  },
];

/**
 * ChooseAddress component that displays a list of locations for the user to select
 *
 * @param navigation - Navigation prop for navigating between screens
 */
export const ChooseAddress = memo(
  ({
    navigation,
  }: {
    navigation: NavigationProps<RouteName.ChooseAddress>;
  }) => {
    const { t } = useI18n();
    const { setAddress } = usePostTaskStore();
    const { user } = useUserStore();

    // Animation values for each item
    const fadeAnims = useRef<{ [key: string]: Animated.Value }>({}).current;

    const locations = user?.locations || (LOCATION_DATA as IAddress[]);
    /**
     * Handles the selection of an address and navigates to the next screen
     *
     * @param selectedAddress - The location selected by the user
     */
    const onChooseAddress = useCallback(
      async (selectedAddress: IAddress) => {
        if (!selectedAddress) return;

        setAddress(selectedAddress);
        navigation.navigate(RouteName.ChooseService);
      },
      [navigation, setAddress],
    );

    /**
     * Creates or retrieves an animation value for a specific item
     *
     * @param index - The index of the item
     * @returns The animation value for the item
     */
    const getAnimationValue = useCallback(
      (index: number): Animated.Value => {
        const key = index.toString();
        if (!fadeAnims[key]) {
          fadeAnims[key] = new Animated.Value(0);
          // Start the animation with a staggered delay based on index
          Animated.timing(fadeAnims[key], {
            toValue: 1,
            duration: 400,
            delay: index * 100, // Stagger the animations
            useNativeDriver: true,
          }).start();
        }
        return fadeAnims[key];
      },
      [fadeAnims],
    );

    /**
     * Renders a location item in the FlatList with animation
     */
    const renderItem: ListRenderItem<IAddress> = useCallback(
      ({ item, index }) => {
        const fadeAnim = getAnimationValue(index);

        return (
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0], // Slide up from 20px below
                  }),
                },
              ],
            }}
          >
            <LocationItem
              testIDs={{
                item: `address${index + 1}`,
              }}
              shortAddress={item?.shortAddress || ''}
              address={item?.address || ''}
              onPress={() => onChooseAddress(item)}
              // TODO: navigate to edit location screen
              onPressUpdate={() => {}}
            />
          </Animated.View>
        );
      },
      [onChooseAddress, getAnimationValue],
    );

    /**
     * Renders a separator between list items
     */
    const itemSeparatorComponent = useCallback(
      () => <SizedBox height={16} />,
      [],
    );

    /**
     * Key extractor for the FlatList
     */
    const keyExtractor = useCallback(
      (_: IAddress, index: number) => index.toString(),
      [],
    );

    // Apply layout animation when component mounts
    useEffect(() => {
      AnimationHelpers.runLayoutAnimation('easeInEaseOut', 500);
    }, []);

    return (
      <BlockView style={styles.container}>
        <BlockView style={styles.wrapFlatList}>
          <CText
            bold
            style={styles.txtDescription}
          >
            {t('LIST_OF_LOCATIONS')}
          </CText>
          <SizedBox height={8} />
          <FlatList
            data={locations}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            testID={'scrollChooseAddress'}
            ItemSeparatorComponent={itemSeparatorComponent}
            contentContainerStyle={styles.contentContainer}
          />
        </BlockView>
      </BlockView>
    );
  },
);
