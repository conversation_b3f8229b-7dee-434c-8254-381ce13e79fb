/**
 * Component that displays detailed information about the cleaning task
 * including duration, workload, and other service-specific details.
 */
import React, { useMemo } from 'react';
import {
  BlockView,
  Card,
  ColorsV2,
  CText,
  DateTimeHelpers,
  DurationWithGMT,
  IDate,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { Note } from './note';
import styles from './styles';
import { WorkingTime } from './working-time';
/**
 * Displays detailed information about the cleaning task
 */
const Detail: React.FC = () => {
  const { t } = useI18n();
  const { duration, note, pet, isPremium, gender, address, startDate } =
    usePostTaskStore();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  /**
   * Renders workload information based on selected duration
   */
  const shouldRenderDuration = React.useMemo(() => {
    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORK_IN')}</CText>

        <DurationWithGMT
          testID={'duration'}
          style={styles.txtValue}
          timezone={timezone}
          duration={duration}
          date={startDate as IDate}
        />
      </BlockView>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [duration, startDate, timezone]);

  /**
   * Renders pet information if applicable
   */
  const shouldRenderPet = useMemo(() => {
    if (pet && Array.isArray(pet) && pet.length > 0) {
      const pets = pet
        .map((pe) => {
          if (pe?.name) {
            return t(pe.name);
          }
          if (pe?.other) {
            return pe.other;
          }
          return '';
        })
        .filter(Boolean)
        .join(', ');
      if (!pets) return null;
      return (
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('HAVE_PET')}</CText>
          <CText
            testID="havePet"
            style={styles.txtValue}
            numberOfLines={1}
          >
            {pets}
          </CText>
        </BlockView>
      );
    }
    return null;
  }, [pet, t]);

  /**
   * Renders gender preference if applicable
   */
  const shouldRenderGender = useMemo(() => {
    if (!gender) return null;

    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('GENDER')}</CText>
        <CText style={styles.txtValue}>{t(gender)}</CText>
      </BlockView>
    );
  }, [gender, t]);

  /**
   * Renders premium service information if applicable
   */
  const shouldRenderIsPremium = useMemo(() => {
    if (isPremium === true) {
      return (
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('IS_PREMIUM')}</CText>
          <CText
            testID="isPremium"
            style={styles.txtValue}
          >
            {t('TASK_PREMIUM')}
          </CText>
        </BlockView>
      );
    }
    return null;
  }, [isPremium, t]);

  return (
    <Card>
      <WorkingTime />
      <SizedBox
        height={1}
        color={ColorsV2.neutral50}
        margin={{ top: Spacing.SPACE_12 }}
      />
      <BlockView style={styles.wrapDetail}>
        <CText
          bold
          style={styles.subPanel}
        >
          {t('TASK_DETAIL')}
        </CText>
        {shouldRenderDuration}
        {shouldRenderIsPremium}
        {shouldRenderPet}
        {shouldRenderGender}
        <Note note={note ?? ''} />
      </BlockView>
    </Card>
  );
};

export default Detail;
