import React from 'react';
import { BlockView, CText } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from './styles';

export const Note = ({ note }: { note?: string }) => {
  const { t } = useI18n();

  if (!note) {
    return null;
  }

  return (
    <BlockView style={styles.group}>
      <CText style={styles.txtVLabel}>{t('LABEL_NOTE_FOR_TASKER')}</CText>
      <CText
        testID={'taskNote'}
        style={styles.txtValue}
      >
        {note}
      </CText>
    </BlockView>
  );
};
