/**
 * Styles for the Confirm and Payment screen
 *
 * This file contains all the styling for the confirmation and payment screen
 * including layout, spacing, colors, and typography.
 */
import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

/**
 * StyleSheet for the Confirm and Payment screen
 */
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BG_COLOR,
  },
  content: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: 150,
    backgroundColor: Colors.WHITE,
  },
  lineWithBorder: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.BORDER_COLOR,
    marginBottom: Spacing.SPACE_12,
    paddingBottom: Spacing.SPACE_12,
  },
  txtAC: {
    marginBottom: Spacing.SPACE_04,
  },
  panel: {
    marginTop: Spacing.SPACE_32,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_18,
  },
  subPanel: {
    marginBottom: Spacing.SPACE_12,
    color: Colors.BLACK,
  },
  label: {
    color: Colors.GREY,
  },
  txtVLabel: {
    width: '35%',
    paddingRight: Spacing.SPACE_12,
    color: Colors.GREY,
  },
  txtValue: {
    width: '65%',
    textAlign: 'right',
  },
  txtValue2: {
    textAlign: 'right',
  },
  group: {
    flexDirection: 'row',
    paddingVertical: Spacing.SPACE_04,
    // alignItems: 'center',
  },
  txtAction: {
    color: Colors.SECONDARY_COLOR,
  },
  txtAmountDryClean: {
    color: Colors.PRIMARY_COLOR,
  },
  txtDryClean: {
    textAlign: 'right',
    color: Colors.BLACK_2,
  },
  txtTermLaundry: {
    color: Colors.GREY,
    padding: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
  },
  btnChange: {
    backgroundColor: Colors.LIGHT_GREY_3,
    justifyContent: 'center',
    alignSelf: 'center',
    paddingVertical: Spacing.SPACE_04,
    paddingHorizontal: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_16,
  },
  wrapLocation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.SPACE_08,
  },
  wrapContact: {
    flex: 1,
    marginTop: Spacing.SPACE_04,
  },
  wrapNumberPhone: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Spacing.SPACE_04,
  },
  containerLocation: {
    flex: 1,
    paddingBottom: Spacing.SPACE_08,
    marginBottom: Spacing.SPACE_08,
  },
  wrapDetail: {
    marginTop: Spacing.SPACE_16,
  },
  pricePanel: {
    marginTop: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPrice: {
    textAlign: 'right',
    color: Colors.BLACK,
  },
  txtPromotion: {
    textDecorationColor: Colors.PRIMARY_COLOR,
    textDecorationLine: 'line-through',
    color: Colors.PRIMARY_COLOR,
    textAlign: 'right',
  },
  txtTotal: {
    color: Colors.BLACK,
  },
  wrapTaskerFavorite: {
    marginBottom: Spacing.SPACE_16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.BORDER_COLOR,
    paddingBottom: Spacing.SPACE_04,
  },
  txtChangeToRegularBooking: {
    color: Colors.SECONDARY_COLOR,
    fontSize: FontSizes.SIZE_16,
  },
  wrapChangeToRegularBooking: {
    marginTop: Spacing.SPACE_20,
  },
  txtChangeToRegularBookingDescription: {
    marginTop: 5,
    fontSize: FontSizes.SIZE_12,
  },
});
