// import locale for dayjs
import 'dayjs/locale/es';
import 'dayjs/locale/id';
import 'dayjs/locale/ko';
import 'dayjs/locale/th';
import 'dayjs/locale/vi';
import 'dayjs/locale/ms';

import dayjs, { OpUnitType } from 'dayjs';
import dayjsPluginCustomParseFormat from 'dayjs/plugin/customParseFormat';
import dayjsPluginIsBetween from 'dayjs/plugin/isBetween';
import dayjsPluginIsSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import dayjsPluginIsSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import dayjsPluginTimezone from 'dayjs/plugin/timezone';
import dayjsPluginUTC from 'dayjs/plugin/utc';
import cloneDeep from 'lodash/cloneDeep';

import { useAppStore } from '../stores';

// dependent plugin for dayjs
dayjs.extend(dayjsPluginUTC);
dayjs.extend(dayjsPluginTimezone);
dayjs.extend(dayjsPluginCustomParseFormat);
dayjs.extend(dayjsPluginIsSameOrBefore);
dayjs.extend(dayjsPluginIsSameOrAfter);
dayjs.extend(dayjsPluginIsBetween);

interface ICity {
  key: 'Jayapura';
  name: 'Jayapura';
  status: 'ACTIVE';
  timezone: 'Asia/Jayapura';
}

export enum TypeFormatDate {
  DateShort = 'DD/MM/YYYY',
  DateFullWithDay = 'dddd, DD/MM/YYYY',
  DateTimeFullWithDay = 'dddd, DD/MM/YYYY - HH:mm',
  DateTimeFullWithDayAndTimeFirst = 'dddd, HH:mm - DD/MM/YYYY',
  HourMinuteDate = 'HH:mm - DD/MM/YYYY',
  DateTimeShort = 'DD/MM/YYYY - HH:mm',
  MonthYearFull = 'MMMM/YYYY',
  TimeHourMinute = 'HH:mm',
  TimeHour = 'HH',
  TimeMinute = 'mm',
  TimeHourMinuteSecond = 'HH:mm:ss',
  DayAbbreviated = 'ddd', // ddd (Tên viết tắt của thứ)
  DayAbbreviatedFull = 'dddd',
  DayOfMonth = 'DD', // DD (Ngày trong tháng)
  IsoDate = 'YYYY-MM-DD', // DD (Ngày trong tháng)
  StartOfYear = 'YYYY-01-01',
  StartOfMonth = 'YYYY-MM-01',
  DateMonth = 'DD/MM',
  DayInMonth = 'Do', // Do -> get ngày trong tháng format 1st
  Day = 'D',
  Days = 'DD',
  Month = 'M',
  Months = 'MM',
  FullMonths = 'MMMM',
  DateMonthDay = 'DD MMMM', // "March 16"
  DateMonthYearFull = 'DD MMMM YYYY',
}

const MINUTES_IN_HOUR = 60;
const MILLISECONDS_IN_MINUTE = 60000;
const MILLISECONDS_IN_SECOND = 1000;
const SECONDS_IN_HOUR = 3600;

export type IDate = dayjs.ConfigType;
export type ITimezone = string;

export class DateTimeHelpers {
  static #supportCity: ICity[] = [];
  static getTzDevice = () => {
    return dayjs.tz.guess();
  };
  static #checkTz = (timezone?: ITimezone) => {
    try {
      if (!timezone) throw new Error();
      // Kiểm tra xem dayjs có thể định dạng thời gian với múi giờ đó hay không
      dayjs().tz(timezone);
      return timezone;
    } catch (e) {
      const tzDevices = this.getTzDevice();
      return tzDevices;
    }
  };
  static setSupportCity = (data: ICity[]) => {
    this.#supportCity = cloneDeep(data) || [];
  };
  static getTimezoneByCity = (city?: string) => {
    if (!city) return this.#checkTz();

    const matchCity = this.#supportCity.find((e) => {
      const isMatch = new RegExp(e.key || '', 'gi').test(city);
      return e.name === city || isMatch;
    });

    return this.#checkTz(matchCity?.timezone);
  };

  /**
   * @description convert timezone name thành GMT+...
   * @param timezone
   * @return Asia/Ho_Chi_Minh -> GMT+7
   */
  static parseTimezoneToGMT = (timezone?: ITimezone) => {
    const date = this.toDayTz({ timezone: this.#checkTz(timezone) });
    const utcOffset = date.utcOffset();
    const gmtOffsetHours = utcOffset / MINUTES_IN_HOUR;
    const result = `GMT${gmtOffsetHours >= 0 ? '+' : ''}${gmtOffsetHours}`;
    return result;
  };

  static convertMilliseconds({ ms }: { ms: number }) {
    // Tính toán phút
    const minutes = Math.floor(ms / MILLISECONDS_IN_MINUTE);
    // Tính toán giây
    const seconds = Math.floor(
      (ms % MILLISECONDS_IN_MINUTE) / MILLISECONDS_IN_SECOND,
    );

    return {
      minutes,
      seconds,
    };
  }
  /**
   * @description So sánh timezone được truyền vào với timezone của device nếu khác thì return text GMT+... của timezone mới
   * @param timezone
   * @return null | GMT+...
   */
  static getGMTByCompareTzDefault = (timezone?: ITimezone) => {
    const tzDevice = this.getTzDevice();
    const firstGMT = this.parseTimezoneToGMT(tzDevice);
    const secondGMT = this.parseTimezoneToGMT(timezone);
    if (firstGMT === secondGMT) {
      return null;
    }
    return `(${secondGMT})`;
  };

  /**
   * @description format date time thành string
   * @param date ngày muốn convert
   * @param dateFormat format format của date
   * @param timezone
   * @param typeFormat muốn format theo đính dạng nào
   * @param keepLocalTime true thì chỉ thay đổi timezone nhưng giữ nguyên giờ
   * @return string
   */
  static formatToString = ({
    date,
    dateFormat,
    timezone,
    typeFormat,
    keepLocalTime,
    locale,
  }: {
    timezone?: ITimezone;
    date?: IDate;
    dateFormat?: TypeFormatDate;
    typeFormat?: TypeFormatDate;
    keepLocalTime?: boolean;
    locale?: string;
  }) => {
    const globalLocale = useAppStore.getState().locale;
    const LOCALE = locale || globalLocale;
    // internalFormat sẽ sao chép tất cả các giá trị từ TypeFormatDate
    const internalFormat = Object.values(TypeFormatDate).reduce(
      (acc, format) => {
        acc[format] = format;
        return acc;
      },
      {} as Record<string, string>,
    );
    // Định nghĩa riêng phần thay đổi trong americanFormat
    const americanFormatOverrides = {
      [TypeFormatDate.DateShort]: 'MM/DD/YYYY',
      [TypeFormatDate.DateFullWithDay]: 'dddd, MM/DD/YYYY',
      [TypeFormatDate.DateTimeFullWithDay]: 'dddd, MM/DD/YYYY - HH:mm',
      [TypeFormatDate.DateTimeShort]: 'MM/DD/YYYY - HH:mm',
      [TypeFormatDate.DateTimeFullWithDayAndTimeFirst]:
        'dddd, HH:mm - MM/DD/YYYY',
      [TypeFormatDate.HourMinuteDate]: 'HH:mm - MM/DD/YYYY',
    };
    // americanFormat kết hợp internalFormat với phần overrides
    const americanFormat: Record<string, string> = {
      ...internalFormat,
      ...americanFormatOverrides,
    };
    const listUseInternalFormat = ['vi', 'th']; //Thêm locale nếu ngôn ngữ nào dùng internal format
    const formatSet = listUseInternalFormat.includes(LOCALE)
      ? internalFormat
      : americanFormat;

    if (!typeFormat) {
      // Return ISO format with timezone when no typeFormat is specified
      return this.toDateTz({
        date,
        format: dateFormat,
        timezone,
        keepLocalTime,
      }).format();
    }

    const template = formatSet[typeFormat];
    return this.toDateTz({ date, format: dateFormat, timezone, keepLocalTime })
      .locale(LOCALE)
      .format(template);
  };
  /**
   * @description convert string sang Dayjs để sử dụng những function liền quan đến date time
   * @param date ngày muốn convert
   * @param format format format của date
   * @param timezone
   * @param keepLocalTime true thì chỉ thay đổi timezone nhưng giữ nguyên giờ
   * @return chuyển [ 1,2,3 ] -> [dateTime, dateTime, dateTime]
   */
  static toDateTz = (params?: {
    date?: IDate;
    format?: TypeFormatDate;
    timezone?: ITimezone;
    keepLocalTime?: boolean;
  }) => {
    return dayjs(params?.date, params?.format).tz(
      this.#checkTz(params?.timezone),
      params?.keepLocalTime,
    );
  };
  /**
   * @description get giờ hiện tại của timezone truyền vào
   * @param timezone
   * @return date time hiện tại
   */
  static toDayTz = ({ timezone }: { timezone?: ITimezone }) => {
    return dayjs().tz(this.#checkTz(timezone));
  };
  /**
   * @description indicating if a date is same or before another date.
   * @param firstDate ngày trước
   * @param secondDate ngày sau
   * @param unit đơn vị so muốn so sánh
   * @return returns a boolean
   * @docs https://day.js.org/docs/en/plugin/is-same-or-before#docsNav
   */
  static checkIsSameOrBefore = ({
    firstDate,
    secondDate,
    timezone,
    unit,
  }: {
    timezone: string;
    firstDate?: IDate;
    secondDate?: IDate;
    unit?: OpUnitType;
  }) => {
    return this.toDateTz({ date: firstDate, timezone }).isSameOrBefore(
      this.toDateTz({ date: secondDate, timezone }),
      unit,
    );
  };
  /**
   * @description indicates whether the Day.js object is before the other supplied date-time.
   * @param firstDate ngày trước
   * @param secondDate ngày sau
   * @param unit đơn vị so muốn so sánh
   * @return returns a boolean
   * @docs https://day.js.org/docs/en/query/is-before#docsNav
   */
  static checkIsBefore = ({
    firstDate,
    secondDate,
    timezone,
    unit,
  }: {
    timezone: string;
    firstDate?: IDate;
    secondDate?: IDate;
    unit?: OpUnitType;
  }) => {
    return this.toDateTz({ date: firstDate, timezone }).isBefore(
      this.toDateTz({ date: secondDate, timezone }),
      unit,
    );
  };
  /**
   * @description indicating if a date is the same or after another date.
   * @param firstDate ngày trước
   * @param secondDate ngày sau
   * @param unit đơn vị so muốn so sánh
   * @return returns a boolean
   * @docs https://day.js.org/docs/en/plugin/is-same-or-after#docsNav
   */
  static checkIsSameOrAfter = ({
    firstDate,
    secondDate,
    timezone,
    unit,
  }: {
    timezone: string;
    firstDate?: IDate;
    secondDate?: IDate;
    unit?: OpUnitType;
  }) => {
    return this.toDateTz({ date: firstDate, timezone }).isSameOrAfter(
      this.toDateTz({ date: secondDate, timezone }),
      unit,
    );
  };
  /**
   * @description indicates whether the Day.js object is after the other supplied date-time.
   * @param firstDate ngày trước
   * @param secondDate ngày sau
   * @param unit đơn vị so muốn so sánh
   * @return returns a boolean
   * @docs https://day.js.org/docs/en/query/is-after#docsNav
   */
  static checkIsAfter = ({
    firstDate,
    secondDate,
    timezone,
    unit,
  }: {
    timezone: string;
    firstDate?: IDate;
    secondDate?: IDate;
    unit?: OpUnitType;
  }) => {
    return this.toDateTz({ date: firstDate, timezone }).isAfter(
      this.toDateTz({ date: secondDate, timezone }),
      unit,
    );
  };
  /**
   * @description indicates whether the Day.js object is after the other supplied date-time.
   * @param firstDate ngày trước
   * @param secondDate ngày sau
   * @param unit đơn vị so muốn so sánh
   * @return returns a boolean
   * @docs https://day.js.org/docs/en/query/is-after#docsNav
   */
  static checkIsSame = ({
    firstDate,
    secondDate,
    timezone,
    unit,
  }: {
    timezone?: string;
    firstDate?: IDate;
    secondDate?: IDate;
    unit?: OpUnitType;
  }) => {
    return this.toDateTz({ date: firstDate, timezone }).isSame(
      this.toDateTz({ date: secondDate, timezone }),
      unit,
    );
  };
  /**
   * @description indicates whether the Day.js object is after the other supplied date-time.
   * @param firstDate ngày trước
   * @param secondDate ngày sau
   * @param unit đơn vị so muốn so sánh
   * @return returns a number
   * @docs https://day.js.org/docs/en/display/difference#docsNav
   */
  static diffDate = ({
    firstDate,
    secondDate,
    timezone,
    unit,
  }: {
    timezone?: string;
    firstDate?: IDate;
    secondDate?: IDate;
    unit?: OpUnitType;
  }) => {
    const toDay = this.toDayTz({ timezone });
    return this.toDateTz({ date: firstDate || toDay, timezone }).diff(
      this.toDateTz({ date: secondDate || toDay, timezone }),
      unit,
    );
  };
  /**
   * @description get ngày của 1 date
   * @return number
   */
  static getDate = ({
    date,
    timezone,
    format,
  }: {
    date?: IDate;
    format?: TypeFormatDate;
    timezone: ITimezone;
  }) => {
    return this.toDateTz({ date, format, timezone }).get('date');
  };
  /**
   * @description get phút của 1 date
   * @return number
   */
  static getMinute = ({
    date,
    timezone,
  }: {
    date?: IDate;
    timezone: ITimezone;
  }) => {
    return this.toDateTz({ date, timezone }).minute();
  };
  /**
   * @description get giờ của 1 date
   * @return number
   */
  static getHour = ({
    date,
    timezone,
  }: {
    date?: IDate;
    timezone: ITimezone;
  }) => {
    return this.toDateTz({ date, timezone }).hour();
  };

  /**
   * @description get tháng của 1 date
   * @return number
   */
  static getMonth = (params?: { date?: IDate; timezone?: ITimezone }) => {
    return this.toDateTz({
      date: params?.date,
      timezone: params?.timezone,
    }).month();
  };

  /**
   * @description indicates whether a date is between two other dates.
   * @param date ngày muốn kiểm tra (if not provided, uses current time)
   * @param startDate ngày bắt đầu
   * @param endDate ngày kết thúc
   * @param timezone múi giờ
   * @param unit đơn vị so sánh (milliseconds, seconds, minutes, hours, days, months, years)
   * @param inclusivity inclusivity '[' means inclusive, '(' exclusive, default is '[]'
   * @return returns a boolean
   * @docs https://day.js.org/docs/en/plugin/is-between#docsNav
   * @example
   * // Check if specific date is between two dates
   * DateTimeHelpers.checkIsBetween({
   *   date: '2023-01-15',
   *   startDate: '2023-01-01',
   *   endDate: '2023-01-31',
   *   timezone: 'Asia/Ho_Chi_Minh'
   * }) // returns true
   *
   * // Check if NOW is between two dates (no date param)
   * DateTimeHelpers.checkIsBetween({
   *   startDate: '2023-01-01',
   *   endDate: '2023-12-31',
   *   timezone: 'Asia/Ho_Chi_Minh'
   * }) // returns true if current time is in 2023
   *
   * // Check with exclusive boundaries
   * DateTimeHelpers.checkIsBetween({
   *   date: '2023-01-01',
   *   startDate: '2023-01-01',
   *   endDate: '2023-01-31',
   *   timezone: 'Asia/Ho_Chi_Minh',
   *   inclusivity: '()'
   * }) // returns false (boundary excluded)
   */
  static checkIsBetween = ({
    date,
    startDate,
    endDate,
    timezone,
    unit,
    inclusivity = '[]',
  }: {
    date?: IDate;
    startDate?: IDate;
    endDate?: IDate;
    timezone?: string;
    unit?: OpUnitType;
    inclusivity?: '()' | '[]' | '[)' | '(]';
  }) => {
    if (!startDate || !endDate) {
      return false;
    }

    // If date is not provided, use current time
    const targetDate = date || this.toDayTz({ timezone });

    return this.toDateTz({ date: targetDate, timezone }).isBetween(
      this.toDateTz({ date: startDate, timezone }),
      this.toDateTz({ date: endDate, timezone }),
      unit,
      inclusivity,
    );
  };

  /**
   * @description Truyền vào thời gian là giây, lấy ra số giờ phút giây
   * @param seconds
   * @example
   * seconds: 3600
   * return "1 gio 0 phut 0 giay"
   */
  // static formatDuration = ({ seconds, locale }: { seconds?: number; locale: string }) => {
  //   if (!seconds) {
  //     return "";
  //   }
  //   const hours = Math.floor(seconds / SECONDS_IN_HOUR);
  //   const minutes = Math.floor((seconds % SECONDS_IN_HOUR) / MINUTES_IN_HOUR);
  //   const remainingSeconds = seconds % MINUTES_IN_HOUR;

  //   const result = [];
  //   if (hours > 0) result.push(i18n.t("CHAT.HOUR", { count: hours, locale }));
  //   if (minutes > 0) result.push(i18n.t("CHAT.MINUTE", { count: minutes, locale }));
  //   if (remainingSeconds >= 0) result.push(i18n.t("CHAT.SECOND", { count: remainingSeconds, locale }));

  //   return result.join(" ");
  // };
}
