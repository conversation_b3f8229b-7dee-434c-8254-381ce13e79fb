import {
  openSettings,
  PERMISSIONS,
  requestMultiple,
  requestNotifications,
  RESULTS,
} from 'react-native-permissions';

import { Alert } from '../helpers';

export class PermissionsService {
  static async checkRequestNotification() {
    const result = await requestNotifications(['alert', 'sound', 'badge']);
    return result.status === 'granted';
  }

  /**
   * Checks and requests camera permission
   * Purpose: Ensures camera access is granted before opening camera functionality
   * @param onGranted - Callback function called when permission is granted
   * @param onCloseNotGranted - Callback function called when permission dialog is closed without granting
   * @returns {void} No return value, performs permission check and request
   */
  static checkRequestCamera = async ({
    onGranted,
    onCloseNotGranted,
  }: {
    onGranted?: () => void;
    onCloseNotGranted?: () => void;
  }): Promise<void> => {
    const cameraPer = [PERMISSIONS.ANDROID.CAMERA, PERMISSIONS.IOS.CAMERA];
    const statuses = await requestMultiple(cameraPer);

    if (
      cameraPer.some((permission) =>
        [RESULTS.GRANTED].includes(statuses[permission] as any),
      )
    ) {
      onGranted?.();
    } else {
      Alert.alert.open?.({
        title: (t) => t('DIALOG_TITLE_INFORMATION'),
        message: (t) => t('PERMISSION.OPEN_SETTING_CAMERA_DES'),
        actions: [
          {
            text: (t) => t('CLOSE'),
            style: 'cancel',
            onPress: () => {
              Alert.alert.close();
            },
          },
          {
            text: (t) => t('PERMISSION.OPEN_SETTING'),
            onPress: () => {
              openSettings();
              Alert.alert.close();
            },
          },
        ],
        onClose: onCloseNotGranted,
      });
    }
  };

  /**
   * Checks and requests photo library permission
   * Purpose: Ensures photo library access is granted before opening gallery functionality
   * @param onGranted - Callback function called when permission is granted
   * @param onCloseNotGranted - Callback function called when permission dialog is closed without granting
   * @returns {void} No return value, performs permission check and request
   */
  static checkRequestPhotoLibrary = async ({
    onGranted,
    onCloseNotGranted,
  }: {
    onGranted?: () => void;
    onCloseNotGranted?: () => void;
  }): Promise<void> => {
    const photoPermission = [
      PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
      PERMISSIONS.ANDROID.READ_MEDIA_VIDEO,
      PERMISSIONS.IOS.PHOTO_LIBRARY,
    ];

    const statuses = await requestMultiple(photoPermission);

    if (
      photoPermission.some((permission) =>
        [RESULTS.GRANTED, RESULTS.LIMITED].includes(
          statuses[permission] as any,
        ),
      )
    ) {
      onGranted?.();
    } else {
      Alert.alert.open?.({
        title: (t) => t('DIALOG_TITLE_INFORMATION'),
        message: (t) => t('PERMISSION.OPEN_SETTING_LIBRARY_DES'),
        actions: [
          {
            text: (t) => t('CLOSE'),
            style: 'cancel',
            onPress: () => {
              Alert.alert.close();
            },
          },
          {
            text: (t) => t('PERMISSION.OPEN_SETTING'),
            onPress: () => {
              openSettings();
              Alert.alert.close();
            },
          },
        ],
        onClose: onCloseNotGranted,
      });
    }
  };

  static checkRequestMicroPermission(
    onCloseNotGranted?: () => void,
  ): Promise<boolean> {
    return new Promise(async (resolve) => {
      try {
        const storagePermission = [
          PERMISSIONS.ANDROID.RECORD_AUDIO,
          PERMISSIONS.IOS.MICROPHONE,
        ];
        const statuses = await requestMultiple(storagePermission);

        const isGranted = storagePermission.some((permission) =>
          [RESULTS.GRANTED, RESULTS.LIMITED].includes(
            statuses[permission] as any,
          ),
        );

        if (isGranted) {
          resolve(true);
          return;
        }

        Alert.alert.open?.(
          {
            title: (t) => t('DIALOG_TITLE_INFORMATION'),
            message: (t) => t('PERMISSION.MICROPHONE_CONTENT'),
            actions: [
              {
                text: (t) => t('CLOSE'),
                style: 'cancel',
                onPress: () => {
                  onCloseNotGranted?.();
                  resolve(false);
                },
              },
              {
                text: (t) => t('PERMISSION.OPEN_SETTING'),
                onPress: () => {
                  openSettings();
                  resolve(false);
                },
              },
            ],
            onClose: () => {
              onCloseNotGranted?.();
              resolve(false);
            },
          },
          true,
        );
      } catch (error) {
        resolve(false);
      }
    });
  }
}
