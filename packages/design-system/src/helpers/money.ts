import { i18n } from '../i18n';
import { useSettingsStore } from '../stores/Settings';
import { formatMoney } from '.';

export const getCurrency = (): string => {
  const currency = useSettingsStore.getState()?.settings?.currency;
  if (!currency) return '';
  return currency?.sign || '';
};

export const showPriceAndCurrency = (price: number) => {
  return i18n.t('COST_AND_CURRENCY', {
    cost: formatMoney(price),
    currency: getCurrency(),
  });
};
