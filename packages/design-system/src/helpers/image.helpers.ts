/**
 * Image helper utilities for the design system
 * Purpose: Provides centralized image handling functionality
 */
export class ImageHelpers {
  /**
   * Checks if an image URI is local (not a remote URL)
   * Purpose: Determines if image needs to be uploaded or is already remote
   * @param uri - Image URI to check
   * @returns {boolean} True if image is local, false if remote
   */
  static isImageLocal = (uri?: string): boolean => {
    if (!uri) return false;

    // Consider file:// URIs as local
    if (uri.startsWith('file://')) return true;

    // Consider content:// URIs as local (Android)
    if (uri.startsWith('content://')) return true;

    // Consider ph:// URIs as local (iOS Photos)
    if (uri.startsWith('ph://')) return true;

    // Consider assets-library:// URIs as local (iOS legacy)
    if (uri.startsWith('assets-library://')) return true;

    // Consider relative paths as local
    if (!uri.startsWith('http://') && !uri.startsWith('https://')) return true;

    return false;
  };

  /**
   * Converts a file path to a proper URI format
   * Purpose: Ensures consistent URI format for image processing
   * @param path - File path to convert
   * @returns {string} Properly formatted URI
   */
  static convertPathToUri = (path: string): string => {
    if (!path) return '';

    // If already a proper URI, return as-is
    if (
      path.startsWith('file://') ||
      path.startsWith('http://') ||
      path.startsWith('https://') ||
      path.startsWith('content://') ||
      path.startsWith('ph://') ||
      path.startsWith('assets-library://')
    ) {
      return path;
    }

    // Convert relative path to file:// URI
    return `file://${path}`;
  };

  /**
   * Extracts filename from a path or URI
   * Purpose: Gets the filename portion from a full path
   * @param path - Path or URI to extract filename from
   * @returns {string} Filename or empty string if not found
   */
  static getNameFromPath = (path?: string): string => {
    if (!path) return '';

    const parts = path.split('/');
    return parts.pop() || '';
  };

  /**
   * Validates if a URI is a valid image format
   * Purpose: Checks if the URI points to a supported image format
   * @param uri - URI to validate
   * @returns {boolean} True if valid image format
   */
  static isValidImageFormat = (uri?: string): boolean => {
    if (!uri) return false;

    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerUri = uri.toLowerCase();

    return imageExtensions.some((ext) => lowerUri.includes(ext));
  };

  /**
   * Gets the file extension from a URI or path
   * Purpose: Extracts file extension for type determination
   * @param uri - URI or path to get extension from
   * @returns {string} File extension (including dot) or empty string
   */
  static getFileExtension = (uri?: string): string => {
    if (!uri) return '';

    const lastDotIndex = uri.lastIndexOf('.');
    const lastSlashIndex = uri.lastIndexOf('/');

    // Make sure the dot is after the last slash (part of filename, not path)
    if (lastDotIndex > lastSlashIndex && lastDotIndex !== -1) {
      return uri.substring(lastDotIndex);
    }

    return '';
  };

  /**
   * Determines MIME type from file extension
   * Purpose: Gets appropriate MIME type for image uploads
   * @param uri - URI or path to determine MIME type for
   * @returns {string} MIME type or default 'image/png'
   */
  static getMimeType = (uri?: string): string => {
    if (!uri) return 'image/png';

    const extension = ImageHelpers.getFileExtension(uri).toLowerCase();

    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.bmp':
        return 'image/bmp';
      case '.webp':
        return 'image/webp';
      default:
        return 'image/png';
    }
  };

  /**
   * Generates a safe filename for upload
   * Purpose: Creates a filename safe for upload with proper extension
   * @param originalName - Original filename
   * @param uri - URI to determine extension if needed
   * @returns {string} Safe filename for upload
   */
  static generateSafeFilename = (
    originalName?: string,
    uri?: string,
  ): string => {
    if (!originalName && !uri) return 'image.png';

    let filename = originalName || 'image';

    // Remove any path separators
    filename = filename.replace(/[/\\]/g, '_');

    // Remove any special characters that might cause issues
    filename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');

    // Ensure we have an extension
    if (!ImageHelpers.getFileExtension(filename) && uri) {
      const extension = ImageHelpers.getFileExtension(uri);
      if (extension) {
        filename += extension;
      } else {
        filename += '.png';
      }
    }

    return filename;
  };
}
