import React from 'react';

import { Icon } from '../components';
import { CToastParams, cToastRef } from '../components/custom-toast';
import { ColorsV2 } from '../tokens';
import { ConfigHelpers } from './config.helpers';

export class ToastHelpers {
  static show = (params: CToastParams) => {
    cToastRef.current?.show({ ...params });
  };

  static showWarning = ({ message }: Pick<CToastParams, 'message'>) => {
    cToastRef.current?.show({
      message,
      contentContainerStyle: {
        backgroundColor: ColorsV2.yellow50,
      },
      messageStyle: {
        textAlign: 'left',
        color: ColorsV2.neutral900,
      },
      icon: (
        <Icon
          name="icWarning"
          size={20}
        />
      ),
    });
  };

  static showSuccess = ({
    message,
    position,
  }: Pick<CToastParams, 'message' | 'position'>) => {
    if (ConfigHelpers.isE2ETesting) return;
    cToastRef.current?.show({
      message,
      position,
      contentContainerStyle: {
        backgroundColor: ColorsV2.green50,
      },
      messageStyle: {
        textAlign: 'left',
        color: ColorsV2.green500,
      },
      icon: (
        <Icon
          name="icCheck"
          size={20}
          color={ColorsV2.green500}
        />
      ),
    });
  };

  static showError = ({
    message,
    position,
  }: Pick<CToastParams, 'message' | 'position'>) => {
    if (ConfigHelpers.isE2ETesting) return;
    cToastRef.current?.show({
      message,
      position,
      contentContainerStyle: {
        backgroundColor: ColorsV2.red50,
      },
      messageStyle: {
        textAlign: 'left',
        color: ColorsV2.red500,
      },
      icon: (
        <Icon
          name="icRemoveCircle"
          size={20}
          color={ColorsV2.red500}
        />
      ),
    });
  };
}
