export class ConfigHelpers {
  //B<PERSON>ến để check là có đang chay e2e test hay không
  static isE2ETesting = false;
  //Biến để lưu route name ban đầu
  static initialRouteName = '';
  //Biến để lưu isoCode
  static isoCode = '';
  //Biến để check là có đang chay e2e test requires user hay không
  static requireUser = null;
  //Biến để set address default
  static requireAddress = null;

  // Set biến isE2ETesting
  static setIsE2ETesting(value: boolean) {
    this.isE2ETesting = value;
  }

  // Set biến initialRouteName
  static setInitialRouteName(value: string) {
    this.initialRouteName = value;
  }

  // Set biến isoCode
  static setIsoCode(value: string) {
    this.isoCode = value;
  }

  // Set biến requireUser
  static setRequireUser(value: any) {
    this.requireUser = value;
  }

  // Set biến requireAddress
  static setRequireAddress(value: any) {
    this.requireAddress = value;
  }
}
