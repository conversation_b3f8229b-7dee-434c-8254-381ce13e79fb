import React, {
  forwardRef,
  isValidElement,
  ReactNode,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Keyboard, StyleSheet } from 'react-native';

import { useI18n } from '../../hooks';
import { LOCALES } from '../../utils';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';
import {
  BottomModal,
  BottomModalHandle,
  BottomModalProps,
} from '../bottom-modal';
import { CText } from '../text';
import { CommonTextProps } from '../text/types';
import { styles } from './styles';

type AlertComponentProps = {};
export type AlertComponentHandle = {
  open: (data: IStateAlert, onlyKey?: boolean) => void;
  close: () => void;
};

type ITextTranslateFunc<T> = (
  t: ReturnType<typeof useI18n>['t'],
  locale: LOCALES,
) => T | undefined;

export type IStateAlert = Pick<
  BottomModalProps,
  | 'titleStyle'
  | 'subTitleStyle'
  | 'onClose'
  | 'isColumnButton'
  | 'isDisabledBackdropPress'
  | 'backgroundContainer'
> & {
  testIDs?: {
    title?: string;
    subTitle?: string;
    message?: string;
  };
  /**
   * String | 1 function return string
   * ```
   * @ex1 message = 'bTaskee'
   * @ex2 message = (t, locale) => t('BTASKEE', {t1: locale})
   * ```
   */
  title?: string | ITextTranslateFunc<string>;

  /**
   * String | 1 function return string
   * @ex1 message = 'bTaskee'
   * @ex2 message = (t, locale) => t('BTASKEE', {t1: locale})
   */
  subTitle?: string | ITextTranslateFunc<string>;

  /**
   * String | ReactNode | 1 function return string hoặc ReactNode
   * @ex1 message = 'bTaskee'
   * @ex2 message = <View><Text>bTaskee</Text></View>
   * @ex3 message = (t, locale) => t('BTASKEE', {t1: locale})
   * @ex4 message = (t, locale) => <View><Text>{t('BTASKEE', {t1: locale})}</Text></View>
   *
   */
  message: string | ReactNode | ITextTranslateFunc<string | ReactNode>;

  /**
   * Array actions
   * @ex1 actions = [{ text: 'Action1', style: 'cancel' }, { text: 'Action2' }]
   * @ex2 actions = [{ text: 'Action1', style: 'cancel' }, { text: 'Action2' }]
   */
  actions?:
    | BottomModalProps['actions']
    | ({ text: ITextTranslateFunc<string> } & Omit<
        NonNullable<BottomModalProps['actions']>[number],
        'text'
      >)[];

  /**
   * Style của content trong modal
   */
  contentContainerStyle?: BlockViewProps['style'];

  /**
   * Style của message
   */
  textStyle?: CommonTextProps['style'];

  /**
   * Nếu muốn truyền thêm các props khác của modal
   */
  bottomSheetProps?: Omit<BottomModalProps, 'children'>;
};

type MessageStringProps = {
  testID?: string;
  style?: CommonTextProps['style'];
} & Pick<CommonTextProps, 'children'>;

const MessageString = ({ testID, children, style }: MessageStringProps) => {
  return (
    <CText
      testID={testID}
      style={StyleSheet.flatten([styles.message, style])}
    >
      {children}
    </CText>
  );
};

export const alertRef = React.createRef<AlertComponentHandle>();
export { BottomSheetModalProvider } from '@gorhom/bottom-sheet';

/**
 * @description Alert global cho toàn App
 * @note import { Alert } from '@helper/alert.helpers';
 */
export const AlertComponent = forwardRef<
  AlertComponentHandle,
  AlertComponentProps
>(({}, ref) => {
  const {
    t,
    i18n: { language },
  } = useI18n('common');
  const locale = language as LOCALES;
  const bottomSheetModalRef = useRef<BottomModalHandle>(null);
  const [state, setState] = useState<IStateAlert>();

  const titleTxt = useMemo(() => {
    return typeof state?.title === 'function'
      ? state.title?.(t, locale)
      : state?.title;
  }, [locale, state, t]);

  const subTitleTxt = useMemo(() => {
    return typeof state?.subTitle === 'function'
      ? state.subTitle?.(t, locale)
      : state?.subTitle;
  }, [locale, state, t]);

  const actions = useMemo<BottomModalProps['actions']>(() => {
    return state?.actions?.map((action) => {
      if (typeof action.text === 'function') {
        action.text = action.text(t, locale) || '';
      }
      return action;
    }) as BottomModalProps['actions'];
  }, [locale, state?.actions, t]);

  /**
   * @description Alert global cho toàn App
   * @note import { Alert } from '@helper/alert.helpers';
   */
  const open: AlertComponentHandle['open'] = (data) => {
    if (!data.message) return;

    Keyboard.dismiss();

    // Auto-close existing alert before opening new one
    if (state) {
      bottomSheetModalRef.current?.close();

      // Wait for close animation to complete before showing new alert
      setTimeout(() => {
        setState(data);
        bottomSheetModalRef.current?.present();
      }, 300); // 300ms should be enough for most closing animations
    } else {
      // No existing alert, show immediately
      setState(data);
      bottomSheetModalRef.current?.present();
    }
  };

  const close: AlertComponentHandle['close'] = () => {
    bottomSheetModalRef.current?.close();
  };

  useImperativeHandle(ref, () => ({
    open,
    close,
  }));

  const renderMessage = useMemo(() => {
    // Nếu state?.message là function và return string thì return component messageString callBack t + locale
    if (
      typeof state?.message === 'function' &&
      typeof state?.message(t, locale) === 'string'
    ) {
      return (
        <MessageString
          testID={state?.testIDs?.message}
          style={state.textStyle}
        >
          {state?.message?.(t, locale)}
        </MessageString>
      );
    }

    // Nếu state?.message là string thì return component messageString
    if (typeof state?.message === 'string') {
      return (
        <MessageString
          testID={state?.testIDs?.message}
          style={state.textStyle}
        >
          {state?.message}
        </MessageString>
      );
    }

    // Nếu state?.message là function và react là 1 Element thì return callBack t + locale
    if (
      typeof state?.message === 'function' &&
      isValidElement(state?.message(t, locale))
    ) {
      return state?.message?.(t, locale);
    }

    // Nếu state?.message là 1 Element thì return chính state?.message
    if (isValidElement(state?.message)) {
      return state?.message;
    }

    return null;
  }, [locale, state, t]);

  return (
    <BottomModal
      modalRef={bottomSheetModalRef}
      enableDynamicSizing={true}
      enablePanDownToClose={true}
      animateOnMount={true}
      index={0}
      title={titleTxt}
      subTitle={subTitleTxt}
      actions={actions}
      testIDs={{
        title: state?.testIDs?.title,
        subTitle: state?.testIDs?.subTitle,
      }}
      onClose={state?.onClose}
      isColumnButton={state?.isColumnButton}
      isDisabledBackdropPress={state?.isDisabledBackdropPress}
      backgroundContainer={state?.backgroundContainer}
      titleStyle={state?.titleStyle}
      {...(state?.bottomSheetProps || {})}
    >
      {/* RENDER MESSAGE */}
      <BlockView
        style={StyleSheet.flatten([
          styles.messageContainer,
          state?.contentContainerStyle,
        ])}
      >
        {renderMessage}
      </BlockView>
      {/* END RENDER MESSAGE */}
    </BottomModal>
  );
});
