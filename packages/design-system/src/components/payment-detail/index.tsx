/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-05-11 14:35:32
 * @modify date 2023-10-17 16:11:25
 * @desc [Detail payment]
 */

import React from 'react';

import { showPriceAndCurrency } from '../../helpers';
import { useI18n } from '../../hooks';
import { BlockView } from '../block-view';
import { Card } from '../card';
import { ConditionView } from '../condition-view';
import { SizedBox } from '../sized-box';
import { CText } from '../text';
import styles from './styles';

const ItemDetail = ({
  title,
  content,
  customStyle,
}: {
  title: string;
  content: string;
  customStyle?: any;
}) => {
  return (
    <BlockView style={styles.group}>
      <CText style={styles.txtVLabel}>{title}</CText>
      <CText style={[styles.txtValue, customStyle]}>{content}</CText>
    </BlockView>
  );
};

const RenderPromotion = ({ price }: { price?: number }) => {
  const { t } = useI18n('common');

  if (price && price > 0) {
    const promotionPriceText = `-${showPriceAndCurrency(price)}`;
    return (
      <ItemDetail
        title={t('FAV_TASKER.VOUCHER_DISCOUNT')}
        content={promotionPriceText}
      />
    );
  }
  return null;
};

export const PaymentDetail = ({ price }: { price: any }) => {
  const { t } = useI18n('common');

  return (
    <BlockView>
      <BlockView
        row
        style={styles.wrapPanel}
      >
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TITLE_PAYMENT_DETAIL')}
        </CText>
      </BlockView>
      <Card style={styles.wrapperPayment}>
        <ItemDetail
          title={t('BASE_COST_SERVICE')}
          content={showPriceAndCurrency(price?.cost - price?.vat)}
        />
        <RenderPromotion price={price?.cost - price?.finalCost} />
        <ConditionView
          condition={Boolean(price?.vat)}
          viewTrue={
            <ItemDetail
              title={t('VAT_COST')}
              content={showPriceAndCurrency(price?.vat)}
            />
          }
        />
        <SizedBox
          height={1}
          style={styles.dividerStyle}
        />
        <ItemDetail
          title={t('TOTAL_PAYMENT')}
          content={showPriceAndCurrency(price?.finalCost)}
          customStyle={styles.txtSuccess}
        />
      </Card>
    </BlockView>
  );
};
