import React, { useEffect, useMemo, useRef } from 'react';
import { Animated, Easing, ViewStyle } from 'react-native';

import { ColorsV2 } from '../../tokens';
import { isIOS } from '../../utils/constant';
import { BlockView } from '../block-view';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

type SwitchProps = {
  testID?: string;
  value?: boolean;
  onValueChange?: (value: boolean) => void;
  disabled?: boolean;
  containerStyle?: ViewStyle;
};

const SIZE_TRACK = 20;
const SIZE_SLIDER = {
  WIDTH: 46,
  HEIGHT: isIOS ? 24 : 16,
};

export const Switch = ({
  testID,
  value,
  onValueChange,
  disabled = false,
  containerStyle = {},
}: SwitchProps) => {
  const trackColor = useMemo(() => {
    if (disabled) {
      return ColorsV2.neutralDisable;
    } else if (value) {
      return ColorsV2.green500;
    } else {
      return ColorsV2.neutral100;
    }
  }, [disabled, value]);

  const sliderColor = disabled
    ? ColorsV2.neutralDisable
    : ColorsV2.neutralWhite;

  const sliderShadow = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  };
  const animatedValue = useRef(
    new Animated.Value(value ? SIZE_SLIDER.WIDTH - SIZE_TRACK - 4 : 0),
  ).current;

  useEffect(() => {
    const duration = 300;
    Animated.timing(animatedValue, {
      toValue: value ? SIZE_SLIDER.WIDTH - SIZE_TRACK - 4 : 0,
      duration,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start();
  }, [value, animatedValue]);

  const toggleSwitch = () => {
    if (!disabled) {
      onValueChange && onValueChange(!value);
    }
  };

  return (
    <TouchableOpacity
      testID={testID}
      onPress={toggleSwitch}
      disabled={disabled}
      activeOpacity={0.7}
      style={containerStyle}
    >
      <BlockView
        justify="center"
        // eslint-disable-next-line react-native/no-inline-styles
        style={{
          width: SIZE_SLIDER.WIDTH,
          height: SIZE_SLIDER.HEIGHT,
          opacity: disabled ? 0.8 : 1,
        }}
      >
        {/* Track (background bar) */}
        <BlockView
          style={[
            styles.slider,
            {
              width: SIZE_SLIDER.WIDTH,
              height: SIZE_SLIDER.HEIGHT,
              borderRadius: SIZE_SLIDER.HEIGHT,
              backgroundColor: trackColor,
            },
          ]}
        />
        {/* Slider (circle) */}
        <Animated.View
          style={[
            // eslint-disable-next-line react-native/no-inline-styles
            {
              position: 'absolute',
              left: 2,
              top: -(SIZE_TRACK - SIZE_SLIDER.HEIGHT) / 2,
              width: SIZE_TRACK,
              height: SIZE_TRACK,
              borderRadius: SIZE_TRACK / 2,
              backgroundColor: sliderColor,
              transform: [{ translateX: animatedValue }],
              ...sliderShadow,
            },
          ]}
        />
      </BlockView>
    </TouchableOpacity>
  );
};
