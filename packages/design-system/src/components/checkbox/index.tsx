import React, { useMemo, useRef, useState } from 'react';
import {
  Animated,
  StyleProp,
  Text,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';

import { IconAssets } from '../../assets';
import { ConditionView } from '../condition-view';
import { IconImage } from '../icon-image';
import { styles } from './styles';

export interface CheckBoxComponentProps {
  testID?: string;
  title?: string | React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  onChecked?: (checked: boolean) => void;
  checked?: boolean;
  disable?: boolean;
  indeterminate?: boolean;
  size?: number;
  keepNormalColor?: boolean;
}

export const CheckBox = (props: CheckBoxComponentProps) => {
  const [isChecked, setIsChecked] = useState(Boolean(props.checked));
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const iconSource = useMemo(() => {
    let icon = IconAssets.icCheckBox;
    if (props.disable && !props?.keepNormalColor) {
      if (props.indeterminate) {
        icon = IconAssets.icIndeterminateDisabled;
      } else if (isChecked) {
        icon = IconAssets.icCheckBoxCheckedDisabled;
      } else {
        icon = IconAssets.icCheckBoxDisabled;
      }
    } else if (props.indeterminate) {
      icon = IconAssets.icIndeterminate;
    } else if (isChecked) {
      icon = IconAssets.icCheckboxChecked;
    } else {
      icon = IconAssets.icCheckBox;
    }
    return icon;
  }, [isChecked, props.disable, props.indeterminate, props?.keepNormalColor]);

  const handleClick = () => {
    // Animate scale down
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    props.onChecked && props.onChecked(!isChecked);
    setIsChecked((pre) => !pre);
  };

  // Listener checked is changed.
  React.useMemo(() => {
    setIsChecked(Boolean(props?.checked));
  }, [props?.checked]);

  return (
    <TouchableOpacity
      disabled={props.disable}
      testID={props?.testID}
      style={[styles.container, props.containerStyle]}
      onPress={handleClick}
      activeOpacity={1}
    >
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <IconImage
          source={iconSource}
          size={props.size ?? 20}
        />
      </Animated.View>
      {props?.title && (
        <ConditionView
          condition={typeof props?.title === 'string'}
          viewTrue={
            <Text style={[styles.txtTitle, props.textStyle]}>
              {props?.title}
            </Text>
          }
          viewFalse={props?.title}
        />
      )}
    </TouchableOpacity>
  );
};
