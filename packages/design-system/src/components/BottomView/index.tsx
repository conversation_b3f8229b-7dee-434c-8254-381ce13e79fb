import React, { PropsWithChildren, useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';

interface BottomViewProps extends BlockViewProps {}

const PADDING_DEFAULT = Spacing.SPACE_16;

export const BottomView = ({
  style,
  children,
  ...props
}: PropsWithChildren<BottomViewProps>) => {
  const insets = useSafeAreaInsets();

  const containerStyle = useMemo(() => {
    return {
      paddingBottom: insets.bottom || PADDING_DEFAULT,
    };
  }, [insets.bottom]);

  return (
    <BlockView
      {...props}
      style={StyleSheet.flatten([styles.container, containerStyle, style])}
    >
      {children}
    </BlockView>
  );
};

const styles = StyleSheet.create({
  container: {},
});
