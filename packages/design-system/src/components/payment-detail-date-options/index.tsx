import React from 'react';

import {
  getFinalCost,
  ITimezone,
  showPriceAndCurrency,
  TypeFormatDate,
} from '../../helpers';
import { useI18n } from '../../hooks';
import { BorderRadius, Colors, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { DateWithGMT } from '../date-with-gmt';
import { RowInfo } from '../row-info';
import { SizedBox } from '../sized-box';
import { CText } from '../text';

export const PaymentDetailDateOptions = ({
  title,
  value,
  testID,
  timezone,
}: {
  testID?: string;
  title?: string;
  value?: any;
  timezone: ITimezone;
}) => {
  const { t } = useI18n('common');

  const finalCost = getFinalCost(value);
  const promotionCost = (value?.costDetail?.cost || 0) - finalCost;

  return (
    <BlockView
      testID={testID}
      backgroundColor={Colors.BG_COLOR}
      margin={{ top: Spacing.SPACE_16 }}
      padding={{
        horizontal: Spacing.SPACE_16,
        top: Spacing.SPACE_16,
        bottom: Spacing.SPACE_04,
      }}
      border={{ width: 1, color: Colors.BORDER_COLOR }}
      radius={BorderRadius.RADIUS_08}
    >
      <BlockView
        row
        jBetween
      >
        <CText
          bold
          color={Colors.PRIMARY_COLOR}
        >
          {title}
        </CText>
      </BlockView>
      <RowInfo
        label={t('COUNTDOWN_DAY')}
        value={
          <DateWithGMT
            timezone={timezone}
            date={value?.eatingTime || value?.date}
            typeFormat={TypeFormatDate.DateTimeFullWithDay}
            style={{ color: Colors.GREY }}
          />
        }
        styleInfo={{ color: Colors.GREY }}
      />
      <RowInfo
        label={t('PRICE_SERVICE')}
        value={showPriceAndCurrency(value?.costDetail?.cost)}
        styleInfo={{ color: Colors.GREY }}
      />
      <ConditionView
        condition={promotionCost > 0}
        viewTrue={
          <RowInfo
            label={t('FAV_TASKER.VOUCHER_DISCOUNT')}
            value={`-${showPriceAndCurrency(promotionCost)}`}
            styleInfo={{ color: Colors.GREY }}
          />
        }
      />
      <SizedBox
        height={1}
        color={Colors.BORDER_COLOR}
      />
      <RowInfo
        label={t('TOTAL_PAYMENT')}
        value={showPriceAndCurrency(finalCost)}
        styleInfo={{ color: Colors.GREEN }}
      />
    </BlockView>
  );
};
