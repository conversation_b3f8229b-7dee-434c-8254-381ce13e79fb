import React, {
  createRef,
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Animated, TextStyle, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { debounce } from 'lodash';

import { ConfigHelpers } from '../../helpers';
import { ColorsV2, Spacing } from '../../tokens';
import { BlockView, ConditionView, CText, Icon, TouchableOpacity } from '..';
import { styles } from './styles';

export type CToastParams = {
  message: string;
  icon?: ReactNode;
  contentContainerStyle?: ViewStyle;
  messageStyle?: TextStyle;
  position?: 'top' | 'bottom';
  isManualClose?: boolean;
};

type CToastProps = {};

type CToastHandle = {
  show: (params: CToastParams) => void;
};

export const cToastRef = createRef<CToastHandle>();

let timeoutId: ReturnType<typeof setTimeout> | null = null;

export const CToast = forwardRef<CToastHandle, CToastProps>((_, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const opacity = useRef(new Animated.Value(0)).current;
  const { bottom, top } = useSafeAreaInsets();
  const [state, setState] = useState<CToastParams>();
  const DURATION_ANIM = ConfigHelpers.isE2ETesting ? 0 : 300;

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  }, []);

  useImperativeHandle(ref, () => ({
    show,
  }));

  const show = useCallback((params: CToastParams) => {
    setState(params);
    setIsVisible(true);
    fadeIn();
    close();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const close = debounce(() => {
    fadeOut(() => {
      setIsVisible(false);
    });
  }, 2000);

  const fadeIn = () => {
    Animated.timing(opacity, {
      toValue: 1,
      delay: 100,
      duration: DURATION_ANIM,
      useNativeDriver: true,
    }).start();
  };

  const fadeOut = (callback: () => void) => {
    Animated.timing(opacity, {
      toValue: 0,
      duration: DURATION_ANIM,
      useNativeDriver: true,
    }).start(callback);
  };

  const positionStyle = useMemo(() => {
    return state?.position === 'top'
      ? { top: top + Spacing.SPACE_12 }
      : { bottom: bottom + Spacing.SPACE_12 };
  }, [bottom, state?.position, top]);

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, { opacity }, positionStyle]}>
      <BlockView
        style={[styles.contentContainer, state?.contentContainerStyle]}
      >
        <ConditionView
          condition={Boolean(state?.icon)}
          viewTrue={
            <BlockView margin={{ right: Spacing.SPACE_08 }}>
              {state?.icon}
            </BlockView>
          }
        />
        <CText
          flex
          center
          color={ColorsV2.neutralWhite}
          style={state?.messageStyle}
        >
          {state?.message}
        </CText>
        <ConditionView
          condition={Boolean(state?.isManualClose)}
          viewTrue={
            <TouchableOpacity
              onPress={close}
              activeOpacity={0.7}
            >
              <Icon
                name={'icRemoveCircle'}
                size={Spacing.SPACE_16}
                color={ColorsV2.neutral300}
              />
            </TouchableOpacity>
          }
        />
      </BlockView>
    </Animated.View>
  );
});
