import { StyleSheet } from 'react-native';

import { ColorsV2, Shadows, Spacing } from '../../tokens';

export const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: ColorsV2.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
    ...Shadows.SHADOW_1,
  },
  pricePanel: {
    marginTop: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPrice: {
    textAlign: 'right',
    color: ColorsV2.neutral800,
  },
  txtPromotion: {
    textDecorationColor: ColorsV2.orange500,
    textDecorationLine: 'line-through',
    color: ColorsV2.orange500,
    textAlign: 'right',
  },
  txtTotal: {
    color: ColorsV2.neutral800,
  },
});
