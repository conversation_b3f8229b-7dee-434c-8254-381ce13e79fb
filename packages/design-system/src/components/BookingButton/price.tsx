import React from 'react';

import { showPriceAndCurrency } from '../../helpers';
import { useI18n } from '../../hooks';
import { IPrice } from '../../types';
import { BlockView } from '../block-view';
import { CText } from '../text';
import { styles } from './styles';

interface PriceProps {
  price?: IPrice | null;
}

const Price: React.FC<PriceProps> = ({ price }) => {
  const { t } = useI18n('common');

  if (!price) {
    return null;
  }

  const priceText = price?.finalCost
    ? showPriceAndCurrency(price?.finalCost)
    : '';

  const hasPromotion =
    price?.cost && price?.finalCost && price?.cost > price?.finalCost;

  const originPriceText = hasPromotion
    ? showPriceAndCurrency(price?.cost || 0)
    : '';

  return (
    <BlockView
      row
      style={styles.pricePanel}
    >
      <BlockView>
        <CText
          bold
          style={styles.txtTotal}
        >
          {t('TOTAL')}
        </CText>
      </BlockView>
      <BlockView>
        {hasPromotion && (
          <CText
            testID="originPrice"
            style={styles.txtPromotion}
          >
            {originPriceText}
          </CText>
        )}
        <CText
          testID="price"
          bold
          style={styles.txtPrice}
        >
          {priceText}
        </CText>
      </BlockView>
    </BlockView>
  );
};

export default Price;
