/**
 * Component that renders the booking button at the bottom of the confirm booking screen.
 * Handles authentication state and booking actions.
 */
import React, { useCallback, useMemo } from 'react';
import Animated, { FadeInDown, FadeOutDown } from 'react-native-reanimated';

import { useI18n } from '../../hooks';
import { useUserStore } from '../../stores';
import { Spacing } from '../../tokens';
import { IPrice, Maybe } from '../../types';
import { BlockView } from '../block-view';
import { PrimaryButton } from '../primary-button';
import { SizedBox } from '../sized-box';
import Price from './price';
import { styles } from './styles';

interface BookingButtonProps {
  testID?: string;
  onPostTask: () => void;
  price: Maybe<IPrice>;
  isDisabled?: boolean;
  navigation: any; // TODO: Replace with proper navigation type
}

/**
 * Button component for confirming booking with price display
 */
export const BookingButton: React.FC<BookingButtonProps> = ({
  testID,
  onPostTask,
  price,
  isDisabled = false,
  navigation,
}) => {
  const { t } = useI18n('common');
  const { user } = useUserStore();

  /**
   * Determines the button text based on authentication state
   */
  const titleBookingButton = useMemo(() => {
    if (!user?._id) {
      return t('SIGN_UP_NOW');
    }
    return t('BUTTON_BOOKING');
  }, [user?._id, t]);

  /**
   * Handles button press - either navigates to auth or proceeds with booking
   */
  const onPressBookingButton = useCallback(() => {
    if (!user?._id) {
      navigation.navigate('Auth', {
        isGoBack: true,
      });
      return;
    }
    onPostTask();
  }, [user?._id, navigation, onPostTask]);

  return (
    <Animated.View
      entering={FadeInDown.duration(500)}
      exiting={FadeOutDown}
    >
      <BlockView
        inset={['bottom']}
        style={styles.container}
      >
        <Price price={price} />
        <SizedBox height={Spacing.SPACE_16} />
        <PrimaryButton
          testID={testID ? testID : 'btnBooking'}
          title={titleBookingButton}
          onPress={onPressBookingButton}
          disabled={isDisabled}
        />
      </BlockView>
    </Animated.View>
  );
};
