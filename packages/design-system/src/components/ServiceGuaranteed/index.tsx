import React from 'react';

import { useI18n } from '../../hooks';
import { Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { Icon } from '../icon';
import { CText } from '../text';
import { styles } from './styles';

export const ServiceGuaranteed = () => {
  const { t } = useI18n('common');
  return (
    <BlockView
      row
      horizontal
      style={styles.wrapShield}
    >
      <Icon
        name="icShield"
        size={Spacing.SPACE_28}
      />
      <BlockView flex>
        <CText
          bold
          style={styles.txtServiceWarranty}
        >
          {t('SERVICE_GUARANTEED')}
        </CText>
      </BlockView>
    </BlockView>
  );
};
