import React, { useMemo } from 'react';
import {
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
} from 'react-native';

import { ColorsV2 } from '../../tokens';
import { ConditionView, IConditionView } from '../condition-view';
import { CText } from '../text';
import { CommonTextProps } from '../text/types';
import { styles } from './styles';

export type PrimaryButtonProps = {
  testID?: string;
  type?: 'secondary' | 'primary';
  title?: string;
  left?: IConditionView['viewTrue'];
  right?: IConditionView['viewTrue'];
  titleProps?: CommonTextProps;
  buttonProps?: TouchableOpacityProps;
  color?: ViewStyle['backgroundColor'];
  titleColor?: TextStyle['color'];
  colorDisabled?: ViewStyle['backgroundColor'];
  colorTitleDisabled?: ViewStyle['backgroundColor'];
} & Pick<TouchableOpacityProps, 'style' | 'onPress' | 'disabled'>;

export const PrimaryButton = ({
  testID,
  title,
  left,
  right,
  style,
  disabled,
  titleProps,
  buttonProps,
  type,
  color = ColorsV2.green500,
  titleColor = ColorsV2.neutralWhite,
  colorDisabled = ColorsV2.neutralDisable,
  colorTitleDisabled = ColorsV2.neutralWhite,
  onPress,
}: PrimaryButtonProps) => {
  const colors = useMemo(() => {
    // Ưu tiên disable
    if (disabled) {
      return {
        background: colorDisabled,
        title: colorTitleDisabled,
      };
    }

    if (type === 'secondary') {
      return {
        background: ColorsV2.neutralSecondary,
        title: ColorsV2.green500,
      };
    }

    return {
      background: color,
      title: titleColor,
    };
  }, [color, colorDisabled, colorTitleDisabled, disabled, titleColor, type]);

  return (
    <TouchableOpacity
      testID={testID}
      activeOpacity={0.7}
      style={StyleSheet.flatten([
        styles.container,
        { backgroundColor: colors.background },
        style,
      ])}
      onPress={onPress}
      disabled={disabled}
      {...buttonProps}
    >
      {
        <ConditionView
          condition={Boolean(left)}
          viewTrue={left}
        />
      }
      <CText
        color={colors.title}
        bold
        size={16}
        {...titleProps}
      >
        {title}
      </CText>
      <ConditionView
        condition={Boolean(right)}
        viewTrue={right}
      />
    </TouchableOpacity>
  );
};
