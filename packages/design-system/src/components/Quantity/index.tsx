/**
 * @Filename: components/add-quantity.js
 * @Description:
 * @CreatedAt: 30/10/2020
 * @Author: DucAnh
 **/

import React, { useState } from 'react';
import {
  Dimensions,
  StyleProp,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from 'react-native';

import { BorderRadius, ColorsV2, FontSizes, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { Icon } from '../icon';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';

const { width } = Dimensions.get('window');

const SIZE_BUTTON = Math.round(width / 10);

interface QuantityProps {
  maxQuantity?: number;
  minQuantity?: number;
  onPressMinus?: () => void;
  onPressPlus?: () => void;
  quantity: number;
  title?: string;
  onChangeValue: (value: number) => void;
  textTitleStyle?: StyleProp<TextStyle>;
  sizeButton?: number;
  containerStyle?: StyleProp<ViewStyle>;
}

export const Quantity = ({
  maxQuantity = 10,
  minQuantity = 1,
  onPressMinus,
  onPressPlus,
  quantity = 0,
  title,
  textTitleStyle = {},
  onChangeValue,
  sizeButton = SIZE_BUTTON,
  containerStyle = {},
}: QuantityProps) => {
  // disabled button Plus

  const [quantityNumber, setQuantityNumber] = useState(quantity);

  const disabledButtonPlus = Boolean(quantityNumber >= maxQuantity);
  const disabledButtonMinus = Boolean(quantityNumber <= minQuantity);

  const handlePressMinus = () => {
    const newQuantity = quantityNumber - 1;
    setQuantityNumber(newQuantity);
    onChangeValue?.(newQuantity);
    onPressMinus?.();
  };

  const handlePressPlus = () => {
    const newQuantity = quantityNumber + 1;
    setQuantityNumber(newQuantity);
    onChangeValue?.(newQuantity);
    onPressPlus?.();
  };

  return (
    <BlockView
      flex
      style={containerStyle}
    >
      <ConditionView
        condition={Boolean(title)}
        viewTrue={
          <BlockView>
            <CText
              bold
              style={[styles.txtTitle, textTitleStyle]}
            >
              {title}
            </CText>
          </BlockView>
        }
      />

      <BlockView
        row
        style={styles.wrap_quantity}
      >
        <TouchableOpacity
          style={[
            styles.btnAction,
            disabledButtonMinus && styles.buttonInactive,
            { width: sizeButton, height: sizeButton },
          ]}
          onPress={handlePressMinus}
          disabled={disabledButtonMinus}
        >
          <Icon
            name={'icSubtractFill'}
            color={ColorsV2.neutral900}
            size={FontSizes.SIZE_18}
          />
        </TouchableOpacity>
        <BlockView style={styles.wrap_txtQuantity}>
          <CText
            bold
            style={styles.txtQuantity}
          >
            {quantityNumber.toString()}
          </CText>
        </BlockView>
        <TouchableOpacity
          style={[
            styles.btnAction,
            disabledButtonPlus && styles.buttonInactive,
            { width: sizeButton, height: sizeButton },
          ]}
          onPress={handlePressPlus}
          disabled={disabledButtonPlus}
        >
          <Icon
            name={'icPlusFill'}
            color={ColorsV2.neutral900}
            size={FontSizes.SIZE_18}
          />
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};

const styles = StyleSheet.create({
  txtTitle: {
    marginVertical: Spacing.SPACE_08,
  },
  buttonInactive: {
    backgroundColor: ColorsV2.neutralBackground,
  },
  btnAction: {
    height: SIZE_BUTTON,
    width: SIZE_BUTTON,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_08,
  },
  wrap_quantity: {
    backgroundColor: ColorsV2.neutral100,
    padding: Spacing.SPACE_08,
    borderRadius: BorderRadius.RADIUS_08,
  },
  txtQuantity: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_18,
  },
  wrap_txtQuantity: {
    flex: 1,
    justifyContent: 'center',
  },
});
