import React, { useMemo, useRef } from 'react';
import {
  Animated,
  StyleProp,
  Text,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';

import { IconAssets } from '../../assets';
import { IconImage } from '../icon-image';
import { styles } from './styles';

export interface RadioProps {
  title?: string;
  checked?: boolean;
  onChecked?: (checked: boolean) => void;
  disable?: boolean;
  size?: number;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  keepNormalColor?: boolean;
}

export const Radio = ({
  title,
  checked,
  onChecked,
  disable,
  size,
  containerStyle,
  textStyle,
  keepNormalColor,
}: RadioProps) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const iconSource = useMemo(() => {
    if (disable && !keepNormalColor) {
      return checked
        ? IconAssets.icRadioCheckedDisabled
        : IconAssets.icRadioDisabled;
    }
    return checked ? IconAssets.icRadioChecked : IconAssets.icRadioUnchecked;
  }, [checked, disable, keepNormalColor]);

  const handlePress = () => {
    if (!disable && onChecked) {
      // Animate scale down and up
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      onChecked(!checked);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={handlePress}
      activeOpacity={disable ? 1 : 0.7}
      disabled={disable}
    >
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <IconImage
          source={iconSource}
          size={size ?? 20}
        />
      </Animated.View>
      {title && (
        <Text style={[styles.title, textStyle, disable && styles.disabledText]}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};
