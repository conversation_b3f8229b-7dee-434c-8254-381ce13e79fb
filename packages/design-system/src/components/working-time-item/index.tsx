import React from 'react';

import { ColorsV2 } from '../../tokens';
import { BlockView } from '../block-view';
import { FastImage, FastImageComponentProps } from '../fast-image';
import { Icon } from '../icon';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

export const ChooseWorkTimeItem = ({
  title,
  onPress,
  image1,
  image2,
  testID,
  content1,
  content2,
}: {
  title: string;
  onPress: () => void;
  image1: FastImageComponentProps['source'];
  image2: FastImageComponentProps['source'];
  testID: string;
  content1: string;
  content2: string;
}) => {
  return (
    <TouchableOpacity
      testID={testID}
      style={styles.blockContent}
      onPress={onPress}
    >
      {/* Top content */}
      <BlockView style={styles.blockTop}>
        {/* Content left */}
        <BlockView style={styles.contentLeft}>
          <CText
            bold
            style={styles.nameMethod}
          >
            {title}
          </CText>
          <CText style={styles.textContent}>{content1}</CText>
        </BlockView>
        {/* Content right */}
        <BlockView style={styles.contentRight}>
          <FastImage
            style={styles.image}
            resizeMode="cover"
            source={image1}
          />
        </BlockView>
      </BlockView>

      {/* Bottom content */}
      <BlockView style={styles.blockBottom}>
        {/* Content left */}
        <BlockView style={styles.contentLeft}>
          <CText style={styles.textContent}>{content2}</CText>
        </BlockView>
        {/* Content right */}
        <BlockView style={styles.contentRight}>
          <FastImage
            style={styles.image}
            resizeMode="cover"
            source={image2}
          />
        </BlockView>
      </BlockView>

      {/* Next icon */}
      <BlockView style={styles.nextIcon}>
        <BlockView style={styles.iconContainer}>
          <Icon
            style={styles.icon}
            color={ColorsV2.neutralWhite}
            resizeMode="cover"
            name={'icNext'}
          />
        </BlockView>
      </BlockView>
    </TouchableOpacity>
  );
};

export default ChooseWorkTimeItem;
