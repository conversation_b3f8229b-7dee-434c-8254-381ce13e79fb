import { StyleSheet } from 'react-native';

import { ColorsV2, Spacing } from '../../tokens';

export const styles = StyleSheet.create({
  blockContent: {
    padding: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
    borderRadius: 16,
    shadowColor: ColorsV2.orange500,
    shadowOffset: {
      width: 1,
      height: 2,
    },
    backgroundColor: ColorsV2.neutralWhite,
    shadowOpacity: 0.3,
    shadowRadius: 3.84,
    elevation: 5,
  },
  blockTop: {
    flexDirection: 'row',
    marginBottom: Spacing.SPACE_16,
  },
  blockBottom: {
    flexDirection: 'row-reverse',
  },
  nextIcon: {
    alignItems: 'flex-end',
    paddingTop: 5,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorsV2.green500,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentLeft: {
    flex: 1,
  },
  contentRight: {
    alignItems: 'flex-end',
    alignSelf: 'center',
    paddingRight: Spacing.SPACE_16,
  },
  nameMethod: {
    paddingTop: 8,
    paddingRight: 24,
    fontSize: 16,
    color: ColorsV2.orange500,
  },
  textContent: {
    lineHeight: 22,
    fontSize: 14,
    paddingTop: 8,
  },
  image: {
    width: 108,
    height: 108,
  },
  icon: {
    width: 24,
    height: 24,
  },
});
