/**
 * Styles for the PriceButton component
 */
import { StyleSheet } from 'react-native';

import { BorderRadius, Colors, ColorsV2, Shadows, Spacing } from '../../tokens';

export default StyleSheet.create({
  loading: {
    alignItems: 'flex-start',
    paddingLeft: '20%',
  },
  txtOriginPrice: {
    textDecorationLine: 'line-through',
    color: Colors.WHITE,
  },
  txtNext: {
    color: Colors.WHITE,
    fontSize: 16,
  },
  txtPrice: {
    color: Colors.WHITE,
    fontSize: 16,
  },
  container: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    right: 0,
    padding: Spacing.SPACE_16,
    ...Shadows.SHADOW_1,
    backgroundColor: Colors.WHITE,
  },
  btnDisabled: {
    backgroundColor: ColorsV2.neutralDisable,
    shadowColor: Colors.GREY,
  },
  left: {
    flex: 1,
    height: 22,
    justifyContent: 'center',
  },
  right: {
    marginLeft: Spacing.SPACE_16,
  },
  btn: {
    ...Shadows.SHADOW_1,
    shadowColor: Colors.SECONDARY_COLOR,
    backgroundColor: Colors.SECONDARY_COLOR,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_16,
  },
  txtSubPriceLaundry: {
    marginTop: Spacing.SPACE_04,
    color: Colors.WHITE,
  },
});
