/**
 * PriceButton Component
 *
 * A button component that displays price information and a call-to-action.
 * It shows the final price, original price (if discounted), and a button label.
 * The component also supports loading state and can be disabled.
 */
import React, { useCallback, useMemo } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
import Animated, { FadeInDown, FadeOutDown } from 'react-native-reanimated';
import { find, get, isEmpty } from 'lodash-es';

import { formatMoney, getCurrency } from '../../helpers';
import { useI18n } from '../../hooks';
import { Colors } from '../../tokens';
import { INCREASE_FEE_KEY, SERVICES } from '../../utils/constant';
import { BlockView } from '../block-view';
import { CText } from '../text';
import styles from './styles';

interface PriceButtonProps {
  isLoading?: boolean;
  IDOriginPrice?: string;
  fromScreen?: string;
  pricePostTask?: any;
  priceUpdate?: any;
  costDetail?: any;
  isChangeBackgroundWhenDisabled?: boolean;
  isDisabled?: boolean;
  IDPrice?: string;
  address?: any;
  curtainLessBasePrice?: any;
  HeaderComponent?: React.ReactNode;
  nextButtonContainer?: ViewStyle;
  isHideVat?: boolean;
  onPress?: () => void;
  testID?: string;
}

/**
 * PriceButton component that displays price information and a call-to-action button
 */
export const PriceButton = React.memo((props: PriceButtonProps) => {
  const {
    isLoading,
    IDOriginPrice,
    fromScreen,
    pricePostTask,
    priceUpdate,
    costDetail,
    isChangeBackgroundWhenDisabled,
    isDisabled,
    IDPrice,
    HeaderComponent = null,
    nextButtonContainer,
    isHideVat,
    onPress,
    testID,
  } = props;

  const { t } = useI18n('common');
  const currency = getCurrency();

  // Determine which price to use based on screen context
  let price = pricePostTask;
  if (fromScreen === 'TASK_UPDATE') {
    price = priceUpdate;
  }

  // If data task not change, get price from task
  if (!price && fromScreen === 'TASK_UPDATE') {
    price = costDetail;
  }

  /**
   * Handle button click
   */
  const handleClick = useCallback(() => {
    onPress && onPress();
  }, [onPress]);

  /**
   * Renders additional price information for laundry service
   */
  const renderSubPriceLaundry = useCallback(() => {
    if (fromScreen && fromScreen === SERVICES.LAUNDRY) {
      const increaseReasons = get(price, 'increaseReasons', []);
      const emergency = find(increaseReasons, {
        key: INCREASE_FEE_KEY.EMERGENCY,
      });
      return (
        <BlockView left>
          <CText style={styles.txtSubPriceLaundry}>
            {t('INCLUDE_FEE_SHIP')}
          </CText>
          {emergency ? (
            <CText
              style={styles.txtSubPriceLaundry}
              testID={'lbEmergencyFee'}
            >
              {t('LAUNDRY_EMERGENCY_FEE', {
                t1: formatMoney(emergency.value),
                t2: currency,
              })}
            </CText>
          ) : null}
        </BlockView>
      );
    }
    return null;
  }, [fromScreen, price, currency, t]);

  /**
   * Renders the price button with all its content
   */
  const renderPriceButton = useMemo(() => {
    if (!price && fromScreen !== 'TASK_UPDATE') {
      return null;
    }

    let priceText = '';
    let originPriceText = '';
    // Default VAT cost deduction is 0
    let vatCostDeduction = 0;
    const disabledButton = Boolean(
      isLoading || (price && price.error) || isDisabled,
    );

    /**
     * If VAT should be hidden, calculate the deduction amount
     * The finalCost already includes VAT
     * The VAT amount to deduct is in the vat field of price
     * The pre-VAT amount equals finalCost minus vatCostDeduction
     */
    if (isHideVat) {
      vatCostDeduction = price?.vat;
    }

    // Format the final price text
    if (price?.finalCost) {
      priceText = `${t('COST_AND_CURRENCY', {
        cost: formatMoney(price.finalCost - vatCostDeduction),
        currency: currency,
      })}`;
    }

    // Add duration if available
    if (price?.duration) {
      priceText += `/${price.duration}h`;
    }

    // Format original price if there's a promotion
    if (price && price.cost > price.finalCost) {
      originPriceText = `${t('COST_AND_CURRENCY', {
        cost: formatMoney(price.cost - vatCostDeduction),
        currency: currency,
      })}`;
    }

    // Determine button title based on screen context
    let titleButton = t('NEXT');
    if (fromScreen === 'TASK_UPDATE') {
      titleButton = t('UPDATE');
    }

    return (
      <Animated.View
        style={[styles.container, nextButtonContainer]}
        entering={FadeInDown.duration(500)}
        exiting={FadeOutDown}
      >
        {HeaderComponent}
        <BlockView inset={'bottom'}>
          <TouchableWithoutFeedback
            disabled={disabledButton}
            onPress={handleClick}
          >
            <BlockView
              left
              style={StyleSheet.flatten([
                styles.btn,
                isChangeBackgroundWhenDisabled &&
                  disabledButton &&
                  styles.btnDisabled,
              ])}
            >
              <BlockView
                center
                row
              >
                <BlockView style={styles.left}>
                  {isLoading ? (
                    <BlockView style={styles.loading}>
                      <ActivityIndicator color={Colors.WHITE} />
                    </BlockView>
                  ) : (
                    <BlockView>
                      {originPriceText ? (
                        <CText
                          style={styles.txtOriginPrice}
                          testID={
                            IDOriginPrice ? IDOriginPrice : 'lbOriginPrice'
                          }
                        >
                          {originPriceText}
                        </CText>
                      ) : null}
                      <CText
                        bold
                        style={styles.txtPrice}
                        testID={IDPrice ? IDPrice : 'lbPrice'}
                      >
                        {priceText}
                      </CText>
                    </BlockView>
                  )}
                </BlockView>
                <BlockView style={styles.right}>
                  <CText
                    style={styles.txtNext}
                    testID={testID}
                  >
                    {titleButton}
                  </CText>
                </BlockView>
              </BlockView>
              {renderSubPriceLaundry()}
            </BlockView>
          </TouchableWithoutFeedback>
        </BlockView>
      </Animated.View>
    );
  }, [
    price,
    fromScreen,
    isLoading,
    isDisabled,
    isHideVat,
    t,
    nextButtonContainer,
    HeaderComponent,
    handleClick,
    isChangeBackgroundWhenDisabled,
    IDOriginPrice,
    IDPrice,
    testID,
    renderSubPriceLaundry,
    currency,
  ]);

  // Check if promotion discount 100% cost, show price
  if (isEmpty(price) || !price?.finalCost) {
    return null;
  }

  return renderPriceButton;
});
