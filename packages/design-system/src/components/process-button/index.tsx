import React from 'react';

import { IconAssets } from '../../assets';
import { CText, IconImage, SizedBox, TouchableOpacity } from '../../components';
import { useI18n } from '../../hooks';
import { Spacing } from '../../tokens';
import { styles } from './styles';

type ProcessButtonProps = {
  testID?: string;
  label?: string;
  onPress?: () => void;
};

export const ProcessButton = ({
  testID,
  label,
  onPress,
}: ProcessButtonProps) => {
  const { t } = useI18n('common');
  return (
    <TouchableOpacity
      testID={testID || 'btnTaskDetails'}
      activeOpacity={0.7}
      style={styles.container}
      onPress={onPress}
    >
      <IconImage source={IconAssets.icProcess} />
      <SizedBox width={Spacing.SPACE_16} />
      <CText flex>{label || t('TASK_DETAIL')}</CText>
      <IconImage source={IconAssets.icArrowRight} />
    </TouchableOpacity>
  );
};
