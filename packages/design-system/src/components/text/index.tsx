import React from 'react';
import { StyleSheet, Text as RNText } from 'react-native';

import { ColorsV2, FontFamily, FontSizes } from '../../tokens';
import { createDefaultStyle, handleGutter } from '../../utils/stylesProps';
import { CommonTextProps } from './types';

export const CText = (props: CommonTextProps) => {
  const {
    center,
    bold,
    semiBold,
    underline,
    style,
    size,
    lineHeight,
    color = ColorsV2.neutral800,
    fontFamily = 'medium',
    numberOfLines,
    right,
    left,
    textDecorationStyle,
    backgroundColor,
    padding,
    margin,
    italic,
    flex,
    ...textProps
  } = props;
  const propsStyle = StyleSheet.flatten(style || {});
  const fontSize = propsStyle.fontSize || size || FontSizes.SIZE_14;
  const isBold = propsStyle.fontWeight === 'bold' || bold;

  const textStyle = [
    createDefaultStyle(props),
    padding && handleGutter('padding', padding),
    margin && handleGutter('margin', margin),
    center && { textAlign: 'center' },
    right && { textAlign: 'right' },
    left && { textAlign: 'left' },
    { fontFamily: FontFamily[fontFamily] },
    backgroundColor && { backgroundColor },
    textDecorationStyle && { textDecorationStyle },
    isBold && { fontFamily: FontFamily.bold },
    semiBold && { fontFamily: FontFamily.semiBold },
    underline && { textDecorationLine: 'underline' },
    italic && { fontStyle: 'italic' },
    { color },
    { fontSize },
    { lineHeight: lineHeight || fontSize * 1.5 },
    { ...propsStyle },
  ] as CommonTextProps['style'];

  return (
    <RNText
      style={textStyle}
      numberOfLines={numberOfLines}
      {...textProps}
    />
  );
};
