import { TextProps, TextStyle } from 'react-native';

import { FontFamily, FontSizes } from '../../tokens';
import { DefaultStyleProps } from '../../utils/stylesProps';

export interface CommonTextProps extends DefaultStyleProps, TextProps {
  /**
   *
   * ```
   * size={number} <=> {fontSize: number}
   * ```
   *
   */
  size?: FontSizes;

  /**
   *
   * ```
   * color='string' <=> {color: string}
   * ```
   *
   */
  color?: TextStyle['color'];

  /**
   *
   * ```
   * center <=> { textAlign: 'center' }
   * ```
   *
   */
  center?: boolean;

  /**
   *
   * ```
   * right <=> { textRight: 'right' }
   * ```
   *
   */
  right?: boolean;

  /**
   *
   * ```
   * left <=> { textAlign: 'left' }
   * ```
   *
   */
  left?: boolean;

  /**
   *
   * ```
   * justify <=> { textAlign: 'justify' }
   * ```
   *
   */
  justify?: boolean;

  /**
   *
   * ```
   * fontFamily='string'<=> { fontFamily: string }
   * ```
   *
   */
  fontFamily?: keyof typeof FontFamily; // 'montserratMedium' | 'montserratBold' | 'montserratBoldItalic' | 'montserratMediumItalic';

  /**
   *
   * ```
   * lineHeight={number}<=> { lineHeight: number }
   * ```
   *
   */
  lineHeight?: number;

  /**
   *
   * ```
   * textDecorationStyle = 'solid' <=> { textDecorationStyle : solid }
   * ```
   *
   */
  textDecorationStyle?: TextStyle['textDecorationStyle'];

  /**
   *
   * ```
   * backgroundColor = 'string' <=> { backgroundColor : string }
   * ```
   *
   */
  backgroundColor?: string;

  /**
   * Define how your items are going to **"fill"** over the available space along your main axis
   */
  flex?: boolean | number;

  /**
   * **padding** creates extra space within an component
   */
  padding?: number | GutterProps;

  /**
   * **margin** creates extra space around an component
   */
  margin?: number | GutterProps;

  semiBold?: boolean;
  bold?: boolean;
  underline?: boolean;
  italic?: boolean;
}

export type GutterProps = {
  top?: number;
  left?: number;
  right?: number;
  bottom?: number;
  vertical?: number;
  horizontal?: number;
};
