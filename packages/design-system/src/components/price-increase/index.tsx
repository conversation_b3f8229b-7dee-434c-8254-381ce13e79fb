/**
 * PriceIncrease Component
 *
 * Displays a warning message when price has increased due to supply and demand.
 * Includes smooth fade-in and fade-out animations.
 */
import React, { useCallback, useEffect } from 'react';
import { Animated, ViewStyle } from 'react-native';
import { get } from 'lodash-es';

import {
  AnimationHelpers,
  BlockView,
  BorderRadius,
  Colors,
  CText,
  Icon,
  IPrice,
  Maybe,
  useFadeAnimation,
  useI18n,
} from '../../..';
import { styles } from './styles';

interface PriceIncreaseProps {
  price: Maybe<IPrice>;
  containerStyle?: ViewStyle;
  testID?: string;
}

/**
 * PriceIncrease component
 *
 * Shows a warning when prices have increased due to supply and demand.
 * Animates with fade effects for smooth appearance and disappearance.
 */
export const PriceIncrease = React.memo(
  ({
    price,
    containerStyle = {},
    testID = 'price-increase-warning',
  }: PriceIncreaseProps) => {
    const { t } = useI18n('common');
    const { opacity, isVisible, isAnimating, fadeIn, fadeOut } =
      useFadeAnimation();

    // Check if price has increased
    const checkPriceIncrease = useCallback((priceData: Maybe<IPrice>) => {
      return Boolean(get(priceData, 'isIncrease', false));
    }, []);

    useEffect(() => {
      const shouldShow = checkPriceIncrease(price);

      // Only update if not currently animating
      if (!isAnimating) {
        if (shouldShow && !isVisible) {
          // Run layout animation for smooth container sizing
          AnimationHelpers.runLayoutAnimation();
          fadeIn();
        } else if (!shouldShow && isVisible) {
          fadeOut();
        }
      }
    }, [price, isVisible, isAnimating, checkPriceIncrease, fadeIn, fadeOut]);

    if (!isVisible) {
      return null;
    }

    return (
      <Animated.View
        style={{ opacity }}
        testID={testID}
      >
        <BlockView
          radius={BorderRadius.RADIUS_08}
          border={{ width: 1, color: Colors.ORANGE }}
          style={[styles.container, containerStyle]}
        >
          <Icon
            style={styles.warningIcon}
            name={'icWarning'}
          />
          <BlockView flex>
            <CText style={styles.txtInfo}>
              {t('SUPPLY_DEMAND_COST_INCREASE')}
            </CText>
          </BlockView>
        </BlockView>
      </Animated.View>
    );
  },
);
