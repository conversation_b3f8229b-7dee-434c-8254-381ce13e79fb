import { StyleSheet } from 'react-native';

import { Colors, FontFamily, FontSizes, Spacing } from '../../tokens';

export const styles = StyleSheet.create({
  containerStyle: {
    paddingHorizontal: Spacing.SPACE_04,
    paddingVertical: Spacing.SPACE_08,
  },

  inputStyle: {
    margin: 0,
    padding: 0,
    height: 40,
    color: Colors.BLACK,
    fontSize: FontSizes.SIZE_16,
    fontFamily: FontFamily.medium,
    borderBottomColor: 'transparent',
    marginLeft: 16,
    flex: 1,
  },

  labelStyle: {
    color: Colors.BLACK,
    fontSize: FontSizes.SIZE_14,
    fontWeight: '600',
    fontFamily: FontFamily.medium,
    paddingBottom: Spacing.SPACE_12,
  },

  txtLabel: {
    fontSize: FontSizes.SIZE_14,
  },

  rightIconContainerStyle: {},

  inputContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingBottom: 0,
    borderColor: Colors.LIGHT_GREY_3,
    minHeight: 48,
  },
  errorStyle: {
    marginTop: Spacing.SPACE_08,
    color: Colors.RED,
    fontFamily: FontFamily.medium,
    marginBottom: 0,
  },
  txtError: {
    marginRight: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
    color: Colors.RED,
    textAlign: 'right',
  },
});
