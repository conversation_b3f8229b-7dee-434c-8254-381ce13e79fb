import React, {
  forwardRef,
  ReactNode,
  useImperativeHandle,
  useRef,
} from 'react';
import {
  NativeSyntheticEvent,
  TextInput,
  TextInputFocusEventData,
  TextInputProps,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';

import { Colors } from '../../tokens';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { CText } from '../text';
import { styles } from './styles';

export interface CustomTextInputProps
  extends Omit<
    TextInputProps,
    'ref' | 'onChangeText' | 'onFocus' | 'placeholderTextColor' | 'style'
  > {
  label?: string | ReactNode;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  upperCase?: boolean;
  rightIcon?: ReactNode;
  onFocus?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  onChangeText?: (text: string) => void;
  inputContainerStyle?: ViewStyle;
  errorStyle?: TextStyle;
  containerStyle?: ViewStyle;
  placeholderTextColor?: string;
  error?: string;
  maxLength?: number;
}

export interface CustomTextInputRef {
  focus: () => void;
}

export const CTextInput = forwardRef<CustomTextInputRef, CustomTextInputProps>(
  (props, ref) => {
    const inputRef = useRef<TextInput>(null);

    const {
      label,
      inputStyle,
      labelStyle,
      upperCase,
      rightIcon,
      onFocus,
      onChangeText,
      inputContainerStyle,
      errorStyle,
      containerStyle,
      placeholderTextColor,
      error,
      maxLength,
      ...restProps
    } = props;

    useImperativeHandle(ref, () => ({
      focus() {
        inputRef?.current?.focus?.();
      },
    }));

    let newLabel = label || '';

    // If label is a string and upperCase is true, convert to uppercase
    if (upperCase && typeof newLabel === 'string') {
      newLabel = newLabel.toUpperCase();
    }

    return (
      <BlockView style={[styles.containerStyle, containerStyle]}>
        {newLabel &&
          (typeof newLabel === 'string' ? (
            <CText style={[styles.labelStyle, labelStyle]}>{newLabel}</CText>
          ) : (
            newLabel
          ))}
        <View style={[styles.inputContainerStyle, inputContainerStyle]}>
          <TextInput
            allowFontScaling={false}
            ref={inputRef}
            autoCapitalize="none"
            returnKeyType="done"
            {...restProps}
            onFocus={onFocus}
            autoCorrect={false}
            caretHidden={false}
            maxLength={maxLength}
            underlineColorAndroid={'transparent'}
            selectionColor={Colors.PRIMARY_COLOR}
            onChangeText={onChangeText}
            style={[styles.inputStyle, inputStyle]}
            placeholderTextColor={placeholderTextColor || Colors.GREY}
          />
          {rightIcon}
        </View>
        <ConditionView
          condition={Boolean(error)}
          viewTrue={
            <CText
              right
              style={[styles.errorStyle, errorStyle]}
            >
              {error}
            </CText>
          }
        />
      </BlockView>
    );
  },
);
