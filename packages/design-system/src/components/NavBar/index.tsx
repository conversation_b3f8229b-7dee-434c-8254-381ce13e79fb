import React, { useCallback, useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { ColorsV2, FontSizes, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';
import { ConditionView } from '../condition-view';
import { Icon } from '../icon';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

interface NavBarProps {
  title: string | React.JSX.Element;
  left?: React.JSX.Element;
  right?: React.JSX.Element;
  backgroundColor?: ColorsV2;
  isShadow?: boolean;
  style?: BlockViewProps['style'];
  onGoBack?: () => void;
}

const PADDING_DEFAULT = Spacing.SPACE_12;

export const NavBar = ({
  title,
  left,
  right,
  backgroundColor = ColorsV2.neutralWhite,
  style,
  isShadow = true,
  onGoBack,
}: NavBarProps) => {
  const inset = useSafeAreaInsets();
  const navigation = useNavigation();

  const containerStyle = useMemo<NavBarProps['style']>(() => {
    return {
      paddingTop: inset.top ? inset.top + Spacing.SPACE_04 : PADDING_DEFAULT,
      paddingBottom: PADDING_DEFAULT,
      backgroundColor,
    };
  }, [backgroundColor, inset.top]);

  const shadowStyle = useMemo<NavBarProps['style']>(() => {
    if (isShadow) return {};

    return {
      shadowColor: ColorsV2.neutral900,
      shadowOpacity: 0.1,
      shadowRadius: 6,
      shadowOffset: {
        height: 0,
        width: 0,
      },
      elevation: 1,
    };
  }, [isShadow]);

  const titleElement = useMemo(() => {
    if (!title) return null;

    if (typeof title === 'string') {
      return (
        <CText
          bold
          size={FontSizes.SIZE_20}
        >
          {title}
        </CText>
      );
    }

    return title;
  }, [title]);

  const _onBack = useCallback(() => {
    if (onGoBack) {
      onGoBack();
    } else {
      navigation.goBack();
    }
  }, [navigation, onGoBack]);

  return (
    <BlockView
      style={[
        StyleSheet.flatten([
          styles.container,
          containerStyle,
          shadowStyle,
          style,
        ]),
      ]}
    >
      <ConditionView
        condition={!!left}
        viewTrue={left}
        viewFalse={
          <TouchableOpacity onPress={_onBack}>
            <Icon
              name="icBack"
              size={24}
            />
          </TouchableOpacity>
        }
      />
      <BlockView
        flex
        padding={{ horizontal: Spacing.SPACE_16 }}
      >
        {titleElement}
      </BlockView>
      <ConditionView
        condition={!!right}
        viewTrue={right}
      />
    </BlockView>
  );
};
