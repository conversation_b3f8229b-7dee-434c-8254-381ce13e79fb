import React, { useEffect, useMemo, useState } from 'react';
import { ScrollView } from 'react-native';
import { get } from 'lodash-es';

import {
  DateTimeHelpers,
  IDate,
  ITimezone,
  roundOfNumberMinutes,
  TypeFormatDate,
} from '../../helpers';
import { useI18n } from '../../hooks';
import { Maybe } from '../../types';
import {
  ESTIMATED_TIME_POST_TASK_MINUTES,
  MIN_POST_TASK_TIME,
} from '../../utils';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';
import { CText } from '../text';
import { CommonTextProps } from '../text/types';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

type DateTimeComponentProps = {
  timezone: ITimezone;
  value?: IDate;
  startDate?: IDate;
  limitDate?: Maybe<number>;
  onChange?: (date: IDate, timezone: ITimezone) => void;
  title?: string;
  noShowTitle?: boolean;
  disabled?: boolean;
  settingSystem?: any;
  minDay?: IDate;
  style?: BlockViewProps['style'];
  dateContainerStyle?: BlockViewProps['style'];
  titleDateStyle?: CommonTextProps['style'];
  titleStyle?: CommonTextProps['style'];
};

const LIMIT_DATE = 7;
// get list date
const getRangeWeek = (
  startDate: IDate,
  limitDate: number,
  timezone: ITimezone,
) => {
  const rangeDate = [];
  for (let index = 0; index < limitDate; index++) {
    const temp = DateTimeHelpers.toDateTz({ date: startDate, timezone })
      .add(index, 'day')
      .toDate();
    rangeDate.push(temp);
  }
  return rangeDate;
};

export const DatePicker = (props: DateTimeComponentProps) => {
  const {
    value,
    onChange,
    title,
    noShowTitle,
    settingSystem,
    minDay,
    style,
    titleDateStyle,
    titleStyle = {},
    timezone,
    disabled,
  } = props;

  // get minPostTaskTime from settingSystem
  const minPostTaskTime =
    get(settingSystem, 'minPostTaskTime', MIN_POST_TASK_TIME) +
    ESTIMATED_TIME_POST_TASK_MINUTES;
  const dateValue = DateTimeHelpers.toDateTz({ date: value, timezone });

  const { t } = useI18n('common');
  const [selectedDate, setSelectedDate] = useState(dateValue);
  const [dateIndex, setDateIndex] = useState<number>();
  const [dates, setDates] = useState(
    getRangeWeek(
      props.startDate || dateValue,
      props.limitDate || LIMIT_DATE,
      timezone,
    ),
  );

  // round minutes
  const minute = roundOfNumberMinutes(DateTimeHelpers.getMinute({ timezone }));
  const minDateTimePostTask = DateTimeHelpers.toDayTz({ timezone })
    .startOf('minute')
    .minute(minute)
    .add(minPostTaskTime, 'minute'); // minimum date to post task

  // This func only runs the first time
  useEffect(() => {
    // if value date is null, set default date
    if (!value) {
      // Set date to reducer postTask
      onChangeDate?.(minDateTimePostTask);

      // Set date to show UI
      setSelectedDate(minDateTimePostTask);
    }
  }, []);

  useEffect(() => {
    if (value) {
      // Set start date to array render date
      //Day now and time from value
      const newDefault = DateTimeHelpers.toDayTz({ timezone })
        .hour(dateValue.hour())
        .minute(dateValue.minute())
        .startOf('minute')
        .toDate();
      setDates(
        getRangeWeek(newDefault, props.limitDate || LIMIT_DATE, timezone),
      );
    }
  }, [value, props.limitDate, timezone]);

  useEffect(() => {
    // setSelectedDate(value);
    setDateIndex(
      dates.findIndex((e) => {
        return DateTimeHelpers.checkIsSame({
          timezone,
          firstDate: e,
          secondDate: value,
          unit: 'day',
        });
      }),
    );
  }, [dates, value, timezone]);

  const onChangeDate = (date: IDate) => {
    const dateFormat = DateTimeHelpers.formatToString({ timezone, date });
    onChange && onChange(dateFormat, timezone);
  };

  const handleClick = (date: IDate, index: number) => () => {
    let newDate = DateTimeHelpers.toDateTz({ date, timezone });
    // check choose date > min post task time
    const isSameOrBefore = DateTimeHelpers.checkIsSameOrBefore({
      firstDate: date,
      secondDate: minDateTimePostTask,
      timezone,
    });
    if (isSameOrBefore) {
      // Round 5 minutes
      const roundMinutes = roundOfNumberMinutes(
        DateTimeHelpers.getMinute({ date: minDateTimePostTask, timezone }),
      );

      newDate = minDateTimePostTask.minute(roundMinutes);
    }

    // update date
    setSelectedDate(newDate);

    // update selected index
    // callback
    onChangeDate(newDate);
  };

  const shouldRenderDateTime = useMemo(() => {
    const dateTime = DateTimeHelpers.formatToString({
      timezone,
      date: selectedDate,
      typeFormat: TypeFormatDate.MonthYearFull,
    });

    return (
      <CText
        bold
        style={styles.txtDateTime}
      >
        {dateTime}
      </CText>
    );
  }, [selectedDate, timezone]);

  const shouldRenderWeekday = useMemo(() => {
    return dates.map((date, idx) => {
      const active = Boolean(dateIndex === idx);
      // disabled from suggest subscription
      // minDay from laudry, choose date
      const disabledBtn = Boolean(
        disabled ||
          (minDay &&
            DateTimeHelpers.checkIsSameOrBefore({
              firstDate: date,
              secondDate: minDay,
              timezone,
            })),
      );
      return (
        <TouchableOpacity
          key={`weekdays_${idx}`}
          disabled={disabledBtn}
          style={styles.btnChooseDate}
          onPress={handleClick(date, idx)}
          testID={`weekdays_${idx}`}
        >
          <BlockView
            center
            style={[
              styles.wrapperItem,
              disabledBtn ? styles.disabled : {},
              active ? styles.backgroundActive : {},
            ]}
          >
            <CText
              bold
              style={[styles.txtDay, active && styles.textActive]}
            >
              {DateTimeHelpers.formatToString({
                date,
                timezone,
                typeFormat: TypeFormatDate.DayAbbreviated,
              })}
            </CText>
            <CText
              testID={`days_${DateTimeHelpers.getDate({ date, timezone })}`}
              numberOfLines={1}
              style={[styles.txtWeekday, active && styles.textActive]}
            >
              {DateTimeHelpers.formatToString({
                date,
                timezone,
                typeFormat: TypeFormatDate.DayOfMonth,
              })}
            </CText>
            {idx === 0 ? <BlockView style={styles.today} /> : null}
          </BlockView>
        </TouchableOpacity>
      );
    });
  }, [dateIndex, value, dates, minDay, timezone]);

  return (
    <BlockView>
      {noShowTitle ? null : (
        <CText
          testID="date-picker"
          bold
          style={[styles.panel, titleStyle]}
        >
          {t('WORK_TIME')}
        </CText>
      )}
      <BlockView
        row
        style={[styles.wrapperPanel, style || {}]}
      >
        <CText style={[styles.txtDate, titleDateStyle]}>
          {title ? title : t('CHOOSE_DATE')}
        </CText>
        {shouldRenderDateTime}
      </BlockView>
      <BlockView
        row
        style={[styles.wrapper, props.dateContainerStyle]}
      >
        <ScrollView
          testID="scrollDatePicker"
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{}}
        >
          {shouldRenderWeekday}
        </ScrollView>
      </BlockView>
    </BlockView>
  );
};
