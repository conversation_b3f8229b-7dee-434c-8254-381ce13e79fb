/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:28:34
 * @modify date 2023-11-24 11:28:34
 * @desc Auto choose tasker
 */
import React, { useMemo } from 'react';
import { StyleSheet } from 'react-native';

import { Alert, formatMoney, getCurrency } from '../../helpers';
import { useI18n } from '../../hooks';
import { useAppStore, useSettingsStore } from '../../stores';
import { BorderRadius, Colors, Spacing } from '../../tokens';
import {
  BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
  ISO_CODE,
  OPTIONAL_CHOOSE_TASKER,
  SERVICES,
} from '../../utils';
import { BlockView } from '../block-view';
import { Markdown } from '../markdown';
import { OptionItemPostTask } from '../OptionItemPostTask';
import { CText } from '../text';

interface AutoChooseTaskerProps {
  serviceName: SERVICES;
  setAutoChooseTasker: (value: boolean) => void;
  isAutoChooseTasker: boolean;
}

export const OptionalAutoChooseTasker = ({
  serviceName,
  setAutoChooseTasker,
  isAutoChooseTasker,
}: AutoChooseTaskerProps) => {
  const { t } = useI18n('common');
  const currency = getCurrency();
  const { isoCode } = useAppStore();

  const settings = useSettingsStore().settings;
  const service = settings?.services?.find(
    (item) => item?.name === serviceName,
  );

  const contentDetailChooseTasker = useMemo(() => {
    if (isoCode === ISO_CODE.TH) {
      return t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3_TH');
    }
    return t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3');
  }, [t]);

  const showDescriptionForAutoChooseTasker = () => {
    const feeChoose =
      service?.priceSetting?.costForChooseTasker ||
      settings?.settingSystem?.costForChooseTasker;
    const fee = formatMoney(feeChoose);
    const feeTxt = `${t('PT1_DETAIL_CHOOSE_MANUAL_FEES')}: ${t(
      'PT1_DETAIL_CHOOSE_MANUAL_COST_AND_CURRENCY',
      {
        cost: fee,
        currency: currency,
      },
    )}`;

    return Alert.alert?.open({
      title: t('PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL'),
      message: (
        <BlockView>
          <Markdown
            text={t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_1')}
            paragraphStyle={styles.markdownText}
          />
          <Markdown
            text={t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_2', {
              hour: BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
            })}
            paragraphStyle={styles.markdownText}
          />
          <CText
            bold
            center
            color={Colors.RED}
            padding={{ vertical: Spacing.SPACE_08 }}
          >
            {feeTxt}
          </CText>
          <BlockView
            border={{ width: 1, color: Colors.PRIMARY_COLOR }}
            padding={Spacing.SPACE_16}
            margin={{ top: Spacing.SPACE_16 }}
            radius={BorderRadius.RADIUS_08}
            backgroundColor={Colors.YELLOW_1}
          >
            <CText center>{contentDetailChooseTasker}</CText>
          </BlockView>
        </BlockView>
      ),
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const onValueChange = (checked: boolean) => {
    setAutoChooseTasker(!checked);
    checked ? showDescriptionForAutoChooseTasker() : null;
  };

  return (
    <OptionItemPostTask
      value={!isAutoChooseTasker}
      onValueChange={onValueChange}
      optionalName={OPTIONAL_CHOOSE_TASKER}
      showDescription={showDescriptionForAutoChooseTasker}
    />
  );
};

const styles = StyleSheet.create({
  markdownText: {
    textAlign: 'center',
  },
});
