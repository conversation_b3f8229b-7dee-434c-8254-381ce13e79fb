import React from 'react';

import { IconAssets } from '../../assets';
import { ColorsV2, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';
import { ConditionView } from '../condition-view';
import { IconImage } from '../icon-image';
import { CText } from '../text';
import styles from './styles';

type CustomWarningProps = {
  content?: string;
  ComponentFooter?: React.ReactNode;
} & BlockViewProps;

export const CustomWarning = ({
  content,
  ComponentFooter,
  ...props
}: CustomWarningProps) => {
  if (!content) return null;
  return (
    <BlockView
      style={styles.container}
      {...props}
    >
      <BlockView
        row
        flex
      >
        <IconImage
          style={styles.icon}
          source={IconAssets.icWarning}
          color={ColorsV2.yellow500}
        />
        <BlockView flex>
          <CText style={styles.txtInfo}>{content}</CText>
        </BlockView>
      </BlockView>
      <ConditionView
        condition={Boolean(ComponentFooter)}
        viewTrue={
          <BlockView margin={{ top: Spacing.SPACE_08 }}>
            {ComponentFooter}
          </BlockView>
        }
      />
    </BlockView>
  );
};
