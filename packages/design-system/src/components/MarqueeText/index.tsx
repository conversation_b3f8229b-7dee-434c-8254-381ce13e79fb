/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-11-14 13:47:19
 * @modify date 2024-11-14 13:47:19
 */
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Easing, StyleSheet, TextStyle } from 'react-native';

import { ConfigHelpers } from '../../helpers';
import { ColorsV2, FontFamily } from '../../tokens';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { CText } from '../text';

interface MarqueeTextProps {
  text: string;
  containerWidth: number;
  style?: TextStyle;
}
/**
 * @description: Marquee text theo chiều dọc cho tên của dịch vụ. Dòng 1 sẽ giữ nguyên, từ dòng 2 trở đi sẽ chạy animation
 *
 * props:
 *
 * text: string
 *
 * containerWidth: number
 *
 * style: TextStyle
 */
export const MarqueeText: React.FC<MarqueeTextProps> = ({
  text: newText,
  containerWidth,
  style = {},
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const [line1, setLine1] = useState('');
  const [line2, setLine2] = useState('');
  const [text, setText] = useState(newText);
  const [isTextOverflow, setIsTextOverflow] = useState(false);

  // Kiểm tra và lấy nội dung hai dòng đầu tiên từ văn bản
  const handleTextLayout = (event: any) => {
    const { lines } = event.nativeEvent;

    if (lines.length > 2) {
      // Nếu văn bản có hơn 2 dòng, lấy dòng 1 và gộp phần còn lại cho dòng 2
      setLine1(lines[0].text); // Dòng đầu tiên
      setLine2(
        lines
          .slice(1)
          .map((line: any) => line.text)
          .join(' '),
      ); // Gộp từ dòng thứ 2 trở đi cho hiệu ứng marquee
      setIsTextOverflow(true); // Kích hoạt marquee khi cần thiết
    } else if (lines.length === 2) {
      // Nếu văn bản chỉ có 2 dòng, hiển thị bình thường
      setLine1(lines[0].text);
      setLine2(lines[1].text);
      setIsTextOverflow(lines[1].width > containerWidth);
    } else {
      // Nếu văn bản chỉ có 1 dòng, hiển thị bình thường không cần marquee
      setLine1(lines[0]?.text);
      setLine2('');
      setIsTextOverflow(false);
    }
  };

  // Fix chuyển ngôn ngữ không thay đổi text
  useEffect(() => {
    if (newText) {
      setIsTextOverflow((pre) => !pre);
      setTimeout(() => {
        setIsTextOverflow((pre) => !pre);
      }, 100);
    }
    setText(newText);
  }, [newText]);

  // Hoạt ảnh cho dòng thứ hai khi văn bản quá dài
  useEffect(() => {
    if (isTextOverflow && !ConfigHelpers.isE2ETesting) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: -Math.round(containerWidth * 1.5),
            duration: 10000,
            easing: Easing.linear,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    }
  }, [animatedValue, containerWidth, isTextOverflow]);

  return (
    <BlockView style={[styles.container, { width: containerWidth }]}>
      <ConditionView
        condition={isTextOverflow}
        viewFalse={
          <CText
            style={style}
            onTextLayout={handleTextLayout}
            numberOfLines={3}
          >
            {text}
          </CText>
        }
      />
      <ConditionView
        condition={isTextOverflow}
        viewTrue={
          <>
            <CText
              style={style}
              numberOfLines={1}
            >
              {line1}
            </CText>
            <Animated.Text
              style={[
                style,
                styles.text,
                {
                  width: Math.round(containerWidth * 1.5),
                  transform: [{ translateX: animatedValue }],
                },
              ]}
              numberOfLines={1}
            >
              {line2}
            </Animated.Text>
          </>
        }
      />
    </BlockView>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  text: {
    fontFamily: FontFamily.medium,
    color: ColorsV2.neutral500,
  },
});
