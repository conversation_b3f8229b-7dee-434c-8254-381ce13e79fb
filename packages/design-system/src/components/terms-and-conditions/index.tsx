/**
 * TermsAndConditions Component
 *
 * Displays terms and conditions for air conditioning and sofa cleaning services in Thailand.
 * This component is only rendered for users in Thailand (ISO_CODE.TH).
 */
import React from 'react';

import { useI18n } from '../../hooks';
import { useAppStore } from '../../stores';
import { ISO_CODE } from '../../utils';
import { BlockView } from '../block-view';
import { CText } from '../text';
import { styles } from './styles';

interface TermsAndConditionsProps {
  style?: object;
}

/**
 * Displays terms and conditions for air conditioning and sofa cleaning services in Thailand
 */
export const TermsAndConditions = React.memo(
  ({ style }: TermsAndConditionsProps) => {
    const { t } = useI18n('common');
    const { isoCode } = useAppStore();

    // Early return if not in Thailand
    if (isoCode !== ISO_CODE.TH) {
      return null;
    }

    return (
      <BlockView style={[styles.containerStyle, style]}>
        <CText
          bold
          style={styles.txtTitle}
        >
          {t('TERMS_AND_CONDITIONS_TH')}
        </CText>
        <CText style={styles.txtDescription}>
          {t('TERMS_AND_CONDITIONS_1_TH')}
        </CText>
        <CText style={styles.txtDescription}>
          {t('TERMS_AND_CONDITIONS_2_TH')}
        </CText>
      </BlockView>
    );
  },
);
