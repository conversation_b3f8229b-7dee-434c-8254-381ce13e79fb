import React from 'react';
import { Image, ImageProps } from 'react-native';

import { IconAssets } from '../../assets';
import { Colors, ColorsV2, Spacing } from '../../tokens';

/**
 * Type definition for icon assets
 */
export type IconAsset = {
  [key: string]: number; // Using number type for require() return value
};

/**
 * Props for the Icon component
 */
export interface IconProps extends Omit<ImageProps, 'source'> {
  /**
   * Name of the icon to display
   */
  name: keyof typeof IconAssets;

  /**
   * Size of the icon in pixels
   * @default Spacing.SPACE_20
   */
  size?: number;

  /**
   * Color of the icon
   * @default COLORS.WHITE
   */
  color?: Colors | ColorsV2;

  /**
   * Custom height of the icon in pixels
   * If not provided, will use size prop
   */
  height?: number;

  /**
   * Custom width of the icon in pixels
   * If not provided, will use size prop
   */
  width?: number;
}

/**
 * Icon component for displaying icon assets with customizable size and color
 *
 * @example
 * ```tsx
 * <Icon name="right" size={24} color="primary" />
 * ```
 */
export const Icon: React.FC<IconProps> = ({
  name,
  size = Spacing.SPACE_20,
  color,
  style,
  width,
  height,
}) => {
  const iconSource = IconAssets[name];
  const otherStyle: ImageProps['style'] = {};

  // Khong có icon thì khong hien thi
  if (!iconSource) {
    return null;
  }

  if (color) {
    otherStyle.tintColor = color;
  }

  return (
    <Image
      style={[
        { width: width ?? size, height: height ?? size, ...otherStyle },
        style,
      ]}
      source={iconSource}
    />
  );
};
