/**
 * LocationItem component
 * Displays location information with optional edit functionality
 */
import React, { useCallback, useMemo } from 'react';
import { StyleSheet, ViewStyle } from 'react-native';

import { useI18n } from '../../hooks';
import { Colors, HitSlop } from '../../tokens';
import { COMPANY_TYPE, HomeType, HOUSE_TYPE } from '../../utils';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { Icon } from '../icon';
import { SizedBox } from '../sized-box';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

interface LocationItemProps {
  testIDs?: {
    item?: string;
    editBtn?: string;
  };
  shortAddress?: string;
  address?: string;
  homeType?: HomeType;
  containerStyle?: ViewStyle;
  isShowUpdate?: boolean;
  isHideIcon?: boolean;
  onPress?: () => void;
  onPressUpdate?: () => void;
  iconLeft?: React.JSX.Element;
}

export const LocationItem = React.memo(
  ({
    testIDs,
    shortAddress,
    address,
    homeType,
    containerStyle,
    isHideIcon,
    isShowUpdate,
    onPress,
    onPressUpdate,
    iconLeft,
  }: LocationItemProps) => {
    const { t } = useI18n('common');

    const homeTypeText = useMemo(() => {
      if (!homeType) return null;

      const companyTypes = COMPANY_TYPE.map((e) => e.key);
      let label;

      if (companyTypes.includes(homeType)) {
        label = COMPANY_TYPE.find((e) => e.value === homeType)?.key;
      } else {
        label = HOUSE_TYPE.find((e) => e.value === homeType)?.key;
      }

      return label ? t(label) : null;
    }, [homeType, t]);

    const renderLocationIcon = useCallback(() => {
      if (isHideIcon) return <></>;

      return (
        <>
          <Icon
            name="icLocation"
            size={18}
          />
          <SizedBox width={12} />
        </>
      );
    }, [isHideIcon]);

    const renderHomeTypeText = useCallback(() => {
      if (!homeTypeText) return <></>;

      return (
        <>
          <SizedBox height={5} />
          <CText
            numberOfLines={2}
            color={Colors.GREY}
          >
            {homeTypeText}
          </CText>
        </>
      );
    }, [homeTypeText]);

    const renderEditButton = useCallback(() => {
      if (!isShowUpdate || !onPressUpdate) return <></>;

      return (
        <TouchableOpacity
          testID={testIDs?.editBtn}
          hitSlop={HitSlop.SMALL}
          onPress={onPressUpdate}
          style={styles.updateBtn}
        >
          <Icon
            name="icChange"
            size={20}
          />
        </TouchableOpacity>
      );
    }, [isShowUpdate, onPressUpdate, testIDs?.editBtn]);

    return (
      <BlockView style={StyleSheet.flatten([styles.container, containerStyle])}>
        <TouchableOpacity
          style={styles.leftContainer}
          testID={testIDs?.item}
          disabled={!onPress}
          onPress={onPress}
        >
          <ConditionView
            condition={!isHideIcon}
            viewTrue={renderLocationIcon()}
          />
          <BlockView flex>
            <CText
              size={16}
              bold
              numberOfLines={2}
            >
              {shortAddress}
            </CText>
            <SizedBox height={5} />
            <CText
              numberOfLines={3}
              color={Colors.GREY}
            >
              {address}
            </CText>
            <ConditionView
              condition={Boolean(homeTypeText)}
              viewTrue={renderHomeTypeText()}
            />
          </BlockView>
        </TouchableOpacity>
        <SizedBox width={12} />
        <ConditionView
          condition={Boolean(iconLeft)}
          viewTrue={iconLeft}
          viewFalse={
            <ConditionView
              condition={Boolean(isShowUpdate && onPressUpdate)}
              viewTrue={renderEditButton()}
            />
          }
        />
      </BlockView>
    );
  },
);

// Add display name for debugging
LocationItem.displayName = 'LocationItem';
