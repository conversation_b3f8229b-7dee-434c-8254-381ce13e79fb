import { StyleSheet } from 'react-native';

import {
  BorderRadius,
  Colors,
  FontFamily,
  FontSizes,
  Spacing,
} from '../../../../tokens';

export default StyleSheet.create({
  container: {},
  containerStyleInput: {
    paddingHorizontal: 0,
    marginTop: Spacing.SPACE_08,
  },
  boxListPet: {
    marginTop: Spacing.SPACE_32,
    paddingHorizontal: 10,
  },
  btn: {
    marginTop: Spacing.SPACE_16,
  },
  borderActive: {
    borderColor: Colors.PRIMARY_COLOR,
  },
  textActive: {
    color: Colors.PRIMARY_COLOR,
  },
  content: {
    marginBottom: Spacing.SPACE_32,
  },
  wrapBtn: {
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
  },
  txtPrice: {
    color: Colors.PRIMARY_COLOR,
    fontFamily: FontFamily.bold,
    fontWeight: '700',
  },
  titleStyle: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_24,
  },
});
