/* eslint-disable react-native/no-raw-text */
import React, { createRef, useState } from 'react';

import { IconAssets } from '../../assets';
import { validPhoneNumber } from '../../helpers';
import { useI18n } from '../../hooks';
import { Colors, ColorsV2, FontSizes, Spacing } from '../../tokens';
import { IAddress } from '../../types';
import { BlockView } from '../block-view';
import { Card } from '../card';
import { ConditionView } from '../condition-view';
import { CModal, CModalHandle } from '../custom-modal';
import { CTextInput } from '../custom-text-input';
import { IconImage } from '../icon-image';
import { SizedBox } from '../sized-box';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import styles from './styles';

type LocationPostTaskProps = {
  title?: string;
  address: IAddress;
  homeNumber: string;
  setAddress: (address: IAddress) => void;
};

export const LocationPostTask = ({
  title,
  address,
  homeNumber,
  setAddress,
}: LocationPostTaskProps) => {
  const { t } = useI18n('common');

  const modalRef = createRef<CModalHandle>();
  const [phone, setPhone] = useState(address?.phoneNumber);
  const [name, setName] = useState(address?.contact);

  const _openChangeContactModal = () => {
    // trackingServiceClick({
    //   campaignName: service?.isTet ? configSpecialPreBooking?.name : null,
    //   screenName: TrackingScreenNames.ConfirmPayment,
    //   serviceName: service.name,
    //   action: TRACKING_ACTION.ChangeInfo,
    // });

    modalRef?.current?.open && modalRef?.current?.open();
    setPhone(address?.phoneNumber);
    setName(address?.contact);
  };

  const _changeContactInfo = () => {
    if (phone && name) {
      const newAddress = { ...address };
      newAddress.phoneNumber = phone;
      newAddress.contact = name;

      setAddress && setAddress(newAddress);
    }
  };

  if (!address) {
    return null;
  }

  return (
    <BlockView testID="locationPostTask">
      <BlockView
        style={styles.panel}
        row
      >
        <CText
          bold
          size={FontSizes.SIZE_16}
          style={styles.txtPanel}
        >
          {title ? title : t('LOCATION')}
        </CText>
      </BlockView>
      <Card style={styles.cardStyle}>
        <BlockView row>
          <BlockView>
            <IconImage
              source={IconAssets.icLocation}
              color={ColorsV2.orange500}
            />
          </BlockView>
          <BlockView
            flex
            margin={{ left: Spacing.SPACE_12 }}
          >
            <ConditionView
              condition={Boolean(address?.shortAddress)}
              viewTrue={
                <>
                  <CText
                    testID={'shortAddressPT4'}
                    bold
                    size={FontSizes.SIZE_14}
                  >
                    {address?.shortAddress}
                  </CText>
                  <SizedBox height={Spacing.SPACE_08} />
                </>
              }
            />
            <CText color={Colors.GREY}>{address?.address}</CText>
          </BlockView>
        </BlockView>
        <ConditionView
          condition={Boolean(homeNumber)}
          viewTrue={
            <BlockView
              row
              style={styles.wrapHomeNumber}
            >
              <IconImage
                source={IconAssets.icMess}
                color={ColorsV2.orange500}
              />
              <BlockView
                flex
                margin={{ left: Spacing.SPACE_12 }}
              >
                <CText
                  bold
                  size={FontSizes.SIZE_14}
                >
                  {t('HK_SV_ADDRESS_DETAIL')}
                </CText>
                <SizedBox height={Spacing.SPACE_08} />
                <CText
                  testID="homeNumberPT4"
                  color={Colors.GREY}
                >
                  {homeNumber}
                </CText>
              </BlockView>
            </BlockView>
          }
        />

        <ConditionView
          condition={Boolean(address?.contact && address?.phoneNumber)}
          viewTrue={
            <BlockView
              row
              style={styles.wrapContact}
            >
              <IconImage
                source={IconAssets.icUser}
                color={ColorsV2.orange500}
              />
              <BlockView
                flex
                margin={{ horizontal: Spacing.SPACE_12 }}
              >
                <CText
                  testID="contactNamePT4"
                  bold
                  size={FontSizes.SIZE_14}
                >
                  {address?.contact}
                </CText>
                <SizedBox height={Spacing.SPACE_08} />
                <CText
                  testID={'contactPT4'}
                  color={Colors.GREY}
                >
                  {`(${address?.countryCode}) ${address?.phoneNumber}`}
                </CText>
              </BlockView>
              <BlockView>
                <TouchableOpacity
                  testID="btnChangeContact"
                  onPress={_openChangeContactModal}
                  style={styles.wrapBtn}
                >
                  <CText
                    bold
                    size={FontSizes.SIZE_12}
                    color={Colors.WHITE}
                  >
                    {t('EDIT')}
                  </CText>
                </TouchableOpacity>
              </BlockView>
            </BlockView>
          }
        />
      </Card>
      <CModal
        ref={modalRef}
        avoidKeyboard
        title={t('CONTACT_INFO_MODAL_TITLE')}
        actions={[
          {
            text: t('UPDATE'),
            disabled: !name || !validPhoneNumber(phone, address?.countryCode),
            onPress: _changeContactInfo,
          },
        ]}
      >
        <BlockView>
          <CTextInput
            testID="contactPhoneNumber"
            defaultValue={phone}
            countryCode={address?.countryCode}
            validType="phone"
            label={t('PHONE_NUMBER').toUpperCase()}
            onChangeText={(text: string) => setPhone(text.trim())}
            maxLength={12}
            keyboardType="phone-pad"
          />
          <CTextInput
            testID="contactName"
            defaultValue={name}
            label={t('CONTACT_NAME').toUpperCase()}
            onChangeText={(text: string) => setName(text.trim())}
            maxLength={40}
          />
        </BlockView>
      </CModal>
    </BlockView>
  );
};
