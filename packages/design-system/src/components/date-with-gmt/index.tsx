import React, { useMemo } from 'react';
import { capitalize } from 'lodash';

import {
  DateTimeHelpers,
  IDate,
  ITimezone,
  TypeFormatDate,
} from '../../helpers';
import { CommonTextProps, CText } from '..';

interface DateWithGMTProps extends CommonTextProps {
  timezone?: ITimezone;
  date?: IDate;
  typeFormat: TypeFormatDate;
}

export const DateWithGMT = ({
  timezone,
  date,
  typeFormat,
  ...props
}: DateWithGMTProps) => {
  const text = useMemo(() => {
    if (!date || !timezone) return '';

    let dateFormat = capitalize(
      DateTimeHelpers.formatToString({ timezone, date, typeFormat }),
    );
    const gmt = DateTimeHelpers.getGMTByCompareTzDefault(timezone);
    if (gmt) {
      dateFormat = `${dateFormat} ${gmt}`;
    }
    return dateFormat;
  }, [date, timezone, typeFormat]);

  return <CText {...props}>{text}</CText>;
};
