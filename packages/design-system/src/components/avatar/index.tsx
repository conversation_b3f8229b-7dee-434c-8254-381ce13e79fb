import React, { useMemo } from 'react';
import { StyleProp, StyleSheet, ViewStyle } from 'react-native';
import { FastImageProps } from 'react-native-fast-image';

import { imgPremiumCover } from '../../assets';
import { Colors } from '../../tokens';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { FastImage } from '../fast-image';
import { getAvatar } from './get-avatar';

const DEFAULT_SIZE = 80;
const DEFAULT_INDEX = 0;

/**
 * @see https://github.com/DylanVann/react-native-fast-image
 */
export interface AvatarProps {
  /**
   * Url of avatar
   */
  avatar?: string;

  /**
   * Size of avatar. Default 80px
   */
  size?: number;

  /**
   * Style of box wrap avatar
   */
  containerStyle?: StyleProp<ViewStyle> | undefined;

  /**
   * Resize mode
   */
  resizeMode?: FastImageProps['resizeMode'];

  /**
   * Premium Tasker style of avatar
   */
  isPremiumTasker?: boolean;

  /**
   * Footer Component of avatar
   */
  FooterComponent?: React.JSX.Element;

  /**
   * index of avatar
   */
  index?: any;

  /**
   * Avatar Frame setting of tasker
   */
  avatarFrameSettings?: any;
}

export const Avatar: React.FunctionComponent<AvatarProps> = ({
  containerStyle,
  avatar,
  resizeMode = 'cover',
  isPremiumTasker,
  FooterComponent,
  size = DEFAULT_SIZE,
  index = DEFAULT_INDEX,
  avatarFrameSettings,
}) => {
  const source = getAvatar(avatar);

  // Show Tasker's avatar border depending on the marketing campaign offered. will take out the image in the settings
  const avatarFrame = (() => {
    // Frame được lấy về trong taskerSettings dựa theo event được setup sẵn
    const avatarFrameFromEventPremium = avatarFrameSettings?.premium;
    const avatarFrameFromEventDefault = avatarFrameSettings?.default;

    // Lấy avatarFrameEvent dựa theo isPremiumTasker
    const avatarFrameEvent = isPremiumTasker
      ? avatarFrameFromEventPremium
      : avatarFrameFromEventDefault;

    // Nếu có avatarFrameEvent thì ưu tiên hiển thị avatarFrameEvent
    if (avatarFrameEvent) {
      return { uri: avatarFrameEvent };
    }
    // Lấy avatarFrame theo cách bình thường (Chỉ hiển thị avatarFrame cho Tasker Premium)
    return isPremiumTasker ? imgPremiumCover : null;
  })();

  const sizeAvatarWithPremium = Math.round(
    avatarFrame ? size + size * 0.3 : size,
  );

  const renderAvatar = useMemo(() => {
    return (
      <BlockView center>
        <FastImage
          resizeMode={resizeMode}
          style={StyleSheet.flatten([
            !avatarFrame && styles.borderAvatar,
            styles.image,
            {
              width: size,
              height: size,
            },
          ])}
          source={source}
        />
      </BlockView>
    );
  }, [avatarFrame, size, source, resizeMode]);

  /**
   * Trường hợp Avatar có isShowBorderAvatar sẽ hiển thị LinearGradient thay vì border
   */
  return (
    <BlockView
      center
      style={[
        styles.container,
        { height: size + size * 0.3 },
        containerStyle || {},
      ]}
    >
      <ConditionView
        condition={avatarFrame}
        viewTrue={
          <BlockView
            testID="avatarFrame"
            center
          >
            <FastImage
              testID={`bgAvatarPremium_${index}`}
              resizeMode={resizeMode}
              style={StyleSheet.flatten([
                styles.styleBgAvatarPremium,
                {
                  width: sizeAvatarWithPremium,
                  height: sizeAvatarWithPremium,
                },
              ])}
              source={avatarFrame}
            />
            {renderAvatar}
          </BlockView>
        }
        viewFalse={renderAvatar}
      />
      <ConditionView
        condition={Boolean(FooterComponent)}
        viewTrue={FooterComponent}
      />
    </BlockView>
  );
};

const styles = StyleSheet.create({
  borderAvatar: {
    borderWidth: 1,
    borderColor: Colors.WHITE,
  },
  image: {
    borderRadius: 180,
  },
  container: {
    borderRadius: 180,
  },
  styleBgAvatarPremium: {
    position: 'absolute',
    zIndex: 1,
  },
});
