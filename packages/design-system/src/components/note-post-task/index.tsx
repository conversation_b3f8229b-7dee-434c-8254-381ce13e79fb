/**
 * TaskNoteForPostTask Component
 *
 * A component that allows users to enter notes for a task and optionally apply the note to all tasks.
 * It provides a text input area with a checkbox option to remember the note for future tasks.
 */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { ScrollView, ViewStyle } from 'react-native';

import { AnimationHelpers } from '../../helpers';
import { useI18n } from '../../hooks';
import { Maybe } from '../../types';
import { BlockView } from '../block-view';
import { CheckBox } from '../checkbox';
import { CTextInput, CustomTextInputRef } from '../custom-text-input';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

const MAX_HEIGHT = 32;
let isShowCheckbox = false;
let animationting = false;

interface Service {
  name: string;
  _id: string;
}

interface TaskNoteByService {
  serviceId: string;
  note: string;
}

interface User {
  taskNoteByServiceV3?: TaskNoteByService[];
}

interface TaskNoteForPostTaskProps {
  value?: string;
  isApplyNoteForAllTask?: boolean;
  service?: Maybe<Service>;
  user?: Maybe<User>;
  defaultValue?: string;
  editTask?: boolean;
  autoFocus?: boolean;
  testID?: string;
  containerStyle?: ViewStyle;
  bounces?: boolean;
  isTaskUpdate?: boolean;
  placeholder?: string;
  title: string;
  description: string;
  setNote: (note: string) => void;
  setNoteForAllTask?: (value: boolean) => void;
  setDisablePriceButton?: (value: boolean) => void;
}

export const NotePostTask = React.memo(
  ({
    value,
    isApplyNoteForAllTask,
    service,
    user,
    defaultValue,
    editTask,
    autoFocus,
    setNote,
    setNoteForAllTask,
    setDisablePriceButton,
    placeholder,
    title,
    description,
    testID,
    containerStyle,
    bounces,
    isTaskUpdate,
  }: TaskNoteForPostTaskProps) => {
    const { t } = useI18n('common');

    const [noteText, setNoteText] = React.useState(value || '');
    const [height, setHeight] = React.useState(0);
    const [isRememberNote, setRememberNote] = React.useState(
      Boolean(isApplyNoteForAllTask),
    );
    const [taskNoteDefault, setTaskNoteDefault] = React.useState<
      string | undefined
    >();

    const inputRef = useRef<CustomTextInputRef>(null);
    const timeoutId = useRef<any>(null);

    /**
     * Sets the default task note from user preferences or default value
     */
    const _setTaskNoteDefault = useCallback(() => {
      if (defaultValue && editTask) {
        // save task note to reducer
        setNote(defaultValue);
        setNote(defaultValue);
        setTaskNoteDefault(defaultValue);
        return;
      }

      if (!user || !service) return;

      const taskNoteByServiceV3 = user.taskNoteByServiceV3;
      if (!taskNoteByServiceV3 || !Array.isArray(taskNoteByServiceV3)) return;

      const noteService = taskNoteByServiceV3.find(
        (item) => item.serviceId === service._id,
      );
      if (noteService?.note) {
        // save task note to reducer
        setNote(noteService.note);
        setNote(noteService.note);
        setTaskNoteDefault(noteService.note);
      }
    }, [defaultValue, editTask, service, setNote, user]);

    useEffect(() => {
      _setTaskNoteDefault();
      return () => {
        if (timeoutId.current) {
          clearTimeout(timeoutId.current);
        }
      };
    }, [_setTaskNoteDefault]);

    useEffect(() => {
      if (autoFocus && inputRef.current) {
        inputRef.current.focus();
      }
    }, [autoFocus]);

    /**
     * Saves the note to the reducer
     */
    const setNoteReducer = useCallback(() => {
      if (noteText?.trim()) {
        setNote(noteText.trim());
      }
    }, [noteText, setNote]);

    /**
     * Shows the checkbox with animation
     */
    const showCheckboxWithAnimation = useCallback(() => {
      AnimationHelpers.runLayoutAnimation();
      isShowCheckbox = true;
      animationting = true;
    }, []);

    /**
     * Hides the checkbox with animation
     */
    const hideCheckboxWithAnimation = useCallback(() => {
      AnimationHelpers.runLayoutAnimation();
      isShowCheckbox = false;
      animationting = true;
    }, []);

    /**
     * Handles text changes in the note input
     * @param text - The new text value
     */
    const _onChangeText = useCallback(
      (text: string) => {
        // check note from task update
        if (editTask && setDisablePriceButton && defaultValue !== undefined) {
          setDisablePriceButton(text === defaultValue);
        }

        // if text task note default is changed, show checkBox task note
        if (text !== taskNoteDefault) {
          setHeight(MAX_HEIGHT);
        } else {
          hideCheckboxWithAnimation();
        }

        setNoteText(text);
        setNote(text);

        // show check box
        if (text && text.length > 0 && !isShowCheckbox && !animationting) {
          showCheckboxWithAnimation();
        } else if (text.length === 0) {
          // hide checkbox
          hideCheckboxWithAnimation();
        }
      },
      [
        taskNoteDefault,
        hideCheckboxWithAnimation,
        showCheckboxWithAnimation,
        setNoteText,
        setNote,
        editTask,
        setDisablePriceButton,
        defaultValue,
      ],
    );

    /**
     * Handles checkbox toggle
     */
    const _onCheck = useCallback(() => {
      const newValue = !isRememberNote;
      setRememberNote(newValue);
      if (setNoteForAllTask) {
        setNoteForAllTask(newValue);
      }
    }, [isRememberNote, setNoteForAllTask]);

    /**
     * Determines whether to render the checkbox based on current state
     */
    const shouldRenderCheckBox = useMemo(() => {
      // Height is 0 or task update mode, hide checkbox
      if (height <= 0 || isTaskUpdate) {
        return null;
      }

      return (
        <TouchableOpacity
          testID="checkBoxNoteForAllTask"
          onPress={_onCheck}
          style={styles.touchableRememberNote}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <CheckBox
            onPress={_onCheck}
            checked={isApplyNoteForAllTask}
          />
          <CText style={styles.txtTitleCheckBox}>
            {t('TITLE_CHECKBOX_TASK_NOTE')}
          </CText>
        </TouchableOpacity>
      );
    }, [_onCheck, height, isApplyNoteForAllTask, isTaskUpdate, t]);

    return (
      <ScrollView
        style={[styles.container, containerStyle]}
        bounces={bounces !== false}
        scrollIndicatorInsets={{ right: 1 }}
      >
        <BlockView>
          <CText
            testID="scrollStep3"
            bold
            style={styles.txtNoteForTasker}
          >
            {title}
          </CText>
          <CText style={styles.txtDescription}>{description}</CText>
          <CTextInput
            testID={testID || 'taskNote'}
            ref={inputRef}
            inputStyle={styles.inputStyle}
            placeholder={placeholder}
            multiline
            maxLength={400}
            returnKeyType="default"
            onChangeText={_onChangeText}
            value={noteText}
            containerStyle={styles.containerStyle}
            onSubmitEditing={setNoteReducer}
            onBlur={setNoteReducer}
            textAlignVertical="top"
          />
        </BlockView>
        {shouldRenderCheckBox}
      </ScrollView>
    );
  },
);
