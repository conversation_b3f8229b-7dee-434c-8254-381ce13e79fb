import React from 'react';

import { useI18n } from '../../hooks';
import { FontSizes } from '../../tokens';
import { Avatar } from '../avatar';
import { BlockView } from '../block-view';
import { CText } from '../text';
import styles from './styles';

export const TaskerFavoriteForRebook = ({
  forceTasker,
}: {
  forceTasker: IUser;
}) => {
  const { t } = useI18n('common');
  return (
    <BlockView>
      <CText
        size={FontSizes.SIZE_16}
        bold
      >
        {t('FAV_TASKER.TASKER')}
      </CText>
      <BlockView row>
        <Avatar
          size={50}
          avatar={forceTasker?.avatar}
          isPremiumTasker={forceTasker?.isPremiumTasker}
        />
        <BlockView style={styles.wrapInfo}>
          <CText size={FontSizes.SIZE_16}>{forceTasker?.name}</CText>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
