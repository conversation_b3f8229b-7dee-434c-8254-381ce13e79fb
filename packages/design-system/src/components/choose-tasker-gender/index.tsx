/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:27:46
 * @modify date 2023-11-24 11:27:46
 * @desc Choose pet
 */
import React, { useEffect, useRef, useState } from 'react';
import { isEmpty } from 'lodash-es';

import { Alert } from '../../helpers';
import { useI18n } from '../../hooks';
import { GENDER, OPTIONAL_CHOOSE_GENDER } from '../../utils';
import { BlockView } from '../block-view';
import { CModalHandle } from '../custom-modal';
import { OptionItemPostTask } from '../OptionItemPostTask';
import ModalChooseGender from './modal-choose-gender';

export const OptionalChooseGender = ({
  gender = null,
  setGender,
}: {
  gender?: GENDER | null;
  setGender: (gender: GENDER | null) => void;
}) => {
  const { t } = useI18n('cleaning');
  const modalRef = useRef<CModalHandle>(null);

  const [isGender, setIsGender] = useState(false);

  const showDescriptionChooseGender = () => {
    return Alert.alert?.open?.({
      title: t('BOOKING_STEP_2.WHAT_IS_CHOOSE_GENDER'),
      message: t('BOOKING_STEP_2.DESCRIPTION_CHOOSE_GENDER'),
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const onChangeGender = (value: GENDER | null) => {
    setGender(value);
  };

  const onValueChange = (checked: boolean) => {
    setIsGender(checked);
    // ON
    if (checked) {
      modalRef?.current?.open && modalRef?.current?.open();
    } else {
      // OFF
      onChangeGender(null);
    }
  };

  useEffect(() => {
    setIsGender(!isEmpty(gender));
  }, [gender]);

  return (
    <BlockView>
      <OptionItemPostTask
        value={isGender}
        onValueChange={onValueChange}
        optionalName={OPTIONAL_CHOOSE_GENDER}
        showDescription={showDescriptionChooseGender}
      />
      <ModalChooseGender
        ref={modalRef}
        onSubmit={(gender: GENDER | null) => {
          if (gender) {
            onChangeGender(gender);
          } else {
            // not fill or check to pet option
            setIsGender(false);
          }
        }}
        onCancel={() => {
          setIsGender(false);
        }}
      />
    </BlockView>
  );
};
