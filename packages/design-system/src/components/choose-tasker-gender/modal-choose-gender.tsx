import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Dimensions, StyleSheet } from 'react-native';

import { useI18n } from '../../hooks';
import { BorderRadius, ColorsV2, Spacing } from '../../tokens';
import { GENDER, GENDER_LIST } from '../../utils';
import { BlockView } from '../block-view';
import { CheckBox } from '../checkbox';
import { CModal, CModalHandle } from '../custom-modal';
import { CText } from '../text';

const { width } = Dimensions.get('window');

interface ModalProps {
  onCancel: () => void;
  onSubmit: (value: any) => void;
}

export default forwardRef<CModalHandle, ModalProps>(
  ({ onCancel, onSubmit }, ref) => {
    const { t } = useI18n('cleaning');

    const [gender, setGender] = useState<GENDER | null>(null);

    const modalRef = React.useRef<CModalHandle>(null);

    const _handleClose = () => {
      modalRef?.current?.close && modalRef?.current?.close();
    };

    const _handleOpen = () => {
      setGender(null);
      modalRef?.current?.open && modalRef?.current?.open();
    };

    const selectedGender = (isChecked: boolean, newGender: GENDER) => {
      // add
      if (isChecked) {
        setGender(newGender);
      } else {
        // remove
        setGender(null);
      }
    };

    // can call from parent component, with useRef
    useImperativeHandle(ref, () => ({
      open() {
        _handleOpen();
      },
      close() {
        _handleClose();
      },
    }));

    return (
      <BlockView style={styles.container}>
        <CModal
          ref={modalRef}
          avoidKeyboard
          title={t('BOOKING_STEP_2.CHOOSE_GENDER')}
          actions={[
            {
              text: t('PT1_MAP_POPUP_ADD_PET_SUBMIT'),
              onPress: () => onSubmit(gender),
              testID: 'btnOkPet',
            },
          ]}
          closeModalAction={() => {
            onCancel();
          }}
          onClose={() => {
            onCancel();
          }}
        >
          <BlockView style={styles.content}>
            <CText>{t('BOOKING_STEP_2.DESCRIPTION_CHOOSE_GENDER')}</CText>
            <BlockView
              center
              row
              style={styles.boxListPet}
            >
              {GENDER_LIST.map((genderItem, index) => (
                <CheckBox
                  testID={`Pet${index}`}
                  key={index}
                  checked={Boolean(gender === genderItem)}
                  onChecked={(isChecked) =>
                    selectedGender(isChecked, genderItem)
                  }
                  title={t(`BOOKING_STEP_2.GENDER_${genderItem}`)}
                  textStyle={gender === genderItem ? styles.textActive : {}}
                />
              ))}
            </BlockView>
          </BlockView>
        </CModal>
      </BlockView>
    );
  },
);

const styles = StyleSheet.create({
  container: {},
  containerStyleInput: {
    paddingHorizontal: 0,
  },
  boxListPet: {
    marginTop: Spacing.SPACE_32,
    paddingHorizontal: width * 0.15,
    justifyContent: 'space-between',
  },
  btn: {
    marginTop: Spacing.SPACE_16,
  },
  borderActive: {
    borderColor: ColorsV2.orange500,
  },
  textActive: {
    color: ColorsV2.orange500,
    fontFamily: 'Montserrat-Bold',
    fontWeight: '600',
  },
  content: {
    marginBottom: Spacing.SPACE_16,
  },
  wrapBtn: {
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
  },
});
