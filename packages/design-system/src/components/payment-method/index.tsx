/**
 * Component for displaying and managing payment methods in the booking flow.
 * Allows users to select payment methods and manage promotions.
 */
import React, { useMemo } from 'react';
import { Alert } from 'react-native';

import { showTrueMoneyAccount } from '../../helpers';
import { useI18n } from '../../hooks';
import { Colors, FontSizes } from '../../tokens';
import { NavigationService, RouteName, TYPE_OF_PAYMENT } from '../../utils';
import { BlockView } from '../block-view';
import { Card } from '../card';
import { FastImage } from '../fast-image';
import { Icon } from '../icon';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import styles from './styles';

/**
 * Displays the selected payment method and allows changing it
 */
export const PaymentMethod = React.memo(
  ({
    paymentMethod,
    promotion,
    setPromotion,
    setPaymentMethod,
    blackList,
  }: {
    paymentMethod: any;
    promotion: any;
    setPromotion: (promotion: any) => void;
    setPaymentMethod: (paymentMethod: any) => void;
    blackList: string[];
  }) => {
    const { t } = useI18n('common');

    // set whitelist payment
    const whitelist: any[] = [];

    /**
     * Handles payment method selection
     */
    const choosePaymentMethod = React.useCallback(() => {
      NavigationService.navigateTo(RouteName.Payment, {
        type: TYPE_OF_PAYMENT.bookTask,
        onChoosePaymentMethod: (data: any) => {
          setPaymentMethod(data);
        },
        blackList,
        whitelist,
      });
    }, [blackList, whitelist]);

    /**
     * Handles removing a promotion
     */
    const removePromotion = React.useCallback(() => {
      if (setPromotion) {
        setPromotion(null);
      }
    }, [setPromotion]);

    /**
     * Renders payment method sub-information based on the selected method
     */
    const shouldRenderSubInfo = useMemo(() => {
      let subText: React.ReactNode = null;
      if (paymentMethod?.bankInfo) {
        subText = (
          <CText
            numberOfLines={1}
            style={styles.txtBankName}
          >
            {paymentMethod.bankInfo?.name}
          </CText>
        );
      } else if (paymentMethod?.cardInfo) {
        subText = (
          <CText
            numberOfLines={1}
            style={styles.txtBankName}
          >{`${paymentMethod.cardInfo?.type} - ${paymentMethod.cardInfo?.number}`}</CText>
        );
      } else if (paymentMethod?.walletInfo?.phoneNumber) {
        subText = (
          <CText
            numberOfLines={1}
            style={styles.txtBankName}
          >
            {showTrueMoneyAccount(paymentMethod?.walletInfo?.phoneNumber)}
          </CText>
        );
      }
      return subText;
    }, [
      paymentMethod?.bankInfo,
      paymentMethod?.cardInfo,
      paymentMethod?.walletInfo,
    ]);

    /**
     * Renders promotion information or the promotion selection buttonr
     */
    const shouldRenderPromotionInfo = useMemo(() => {
      if (promotion) {
        return (
          <BlockView style={styles.btnPayment}>
            <Icon
              style={styles.imgIcon}
              resizeMode={'cover'}
              name={'icPromotion'}
            />
            <BlockView style={styles.rightContent}>
              <CText
                numberOfLines={1}
                testID={'txtPromotionCode'}
                style={styles.txtButton}
              >
                {promotion?.code}
              </CText>
            </BlockView>
            <BlockView>
              <TouchableOpacity
                testID="removePromotion"
                onPress={removePromotion}
                style={styles.wrapRemovePromotion}
              >
                <Icon
                  name={'icCloseModal'}
                  size={14}
                  color={Colors.WHITE}
                />
              </TouchableOpacity>
            </BlockView>
          </BlockView>
        );
      }
      return (
        <BlockView style={styles.btnPayment}>
          <Icon
            style={styles.imgIcon}
            resizeMode={'cover'}
            name={'icPromotion'}
          />
          <BlockView style={styles.rightContent}>
            <CText style={styles.txtButton}>{t('TAB_PROMOTION')}</CText>
          </BlockView>
          <BlockView>
            <Icon
              style={styles.imgIconArrow}
              name={'icArrowRight'}
            />
          </BlockView>
        </BlockView>
      );
    }, [promotion, t, removePromotion]);

    return (
      <BlockView>
        <BlockView
          row
          style={styles.panel}
        >
          <CText
            bold
            size={FontSizes.SIZE_16}
          >
            {t('PAYMENT_METHOD')}
          </CText>
        </BlockView>
        <Card style={styles.wrapperPayment}>
          <BlockView style={styles.wrapperBtn}>
            <TouchableOpacity
              testID={'choosePaymentMethod'}
              style={styles.btnPayment}
              onPress={choosePaymentMethod}
            >
              <FastImage
                style={styles.iconPaymentMethod}
                source={paymentMethod?.icon}
              />
              <BlockView style={styles.rightContent}>
                <CText
                  numberOfLines={1}
                  style={styles.txtButton}
                >
                  {paymentMethod?.label ? t(paymentMethod.label) : ''}
                </CText>
                {shouldRenderSubInfo}
              </BlockView>
              <Icon
                style={styles.imgIconArrow}
                name={'icArrowRight'}
              />
            </TouchableOpacity>
          </BlockView>
          <BlockView style={styles.line} />
          <BlockView style={styles.wrapperBtn}>
            <TouchableOpacity
              testID={'promotionCode'}
              onPress={() => Alert.alert('Chọn mã khuyến mãi')}
            >
              {shouldRenderPromotionInfo}
            </TouchableOpacity>
          </BlockView>
        </Card>
      </BlockView>
    );
  },
);
