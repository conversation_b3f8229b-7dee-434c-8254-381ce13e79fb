import React, { forwardRef } from 'react';
import { ScrollView } from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardAwareScrollViewProps,
} from 'react-native-keyboard-controller';

export type KeyboardAwareHandle = ScrollView;

interface KeyboardAwareProps extends KeyboardAwareScrollViewProps {
  extraKeyboardSpace?: number;
  bottomOffset?: number;
  disableScrollOnKeyboardHide?: boolean;
  enabled?: boolean;
  keyboardShouldPersistTaps?: 'handled' | 'never' | 'always';
}

export const KeyboardAware = forwardRef<
  KeyboardAwareHandle,
  KeyboardAwareProps
>(
  (
    {
      children,
      contentContainerStyle,
      style,
      extraKeyboardSpace = 0,
      bottomOffset = 20,
      disableScrollOnKeyboardHide = true,
      enabled = true,
      keyboardShouldPersistTaps = 'handled',
      ...props
    },
    ref,
  ) => {
    return (
      <KeyboardAwareScrollView
        ref={ref}
        style={style}
        contentContainerStyle={contentContainerStyle}
        extraKeyboardSpace={extraKeyboardSpace}
        bottomOffset={bottomOffset}
        disableScrollOnKeyboardHide={disableScrollOnKeyboardHide}
        enabled={enabled}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps={keyboardShouldPersistTaps}
        {...props}
      >
        {children}
      </KeyboardAwareScrollView>
    );
  },
);
