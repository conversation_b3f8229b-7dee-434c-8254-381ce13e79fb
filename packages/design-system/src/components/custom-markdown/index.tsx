import React, { useMemo } from 'react';
import { Text, TextProps } from 'react-native';
import { isEmpty } from 'lodash-es';

import { Colors, FontFamily, FontSizes } from '../../tokens';
import {
  FormatTextWithParams,
  ICustomTextMarkdown,
  IParamsCustomTextMaskDow,
  TextSpecialParams,
} from './type';
/**
 * - param [
    {
      key: "text1",
      value: "ong non",
      style: {
        color: "green",
        fontWeight: "bold",
      },
    },
    {
      key: "text2",
      value: "30/10",
      style: {
        color: "red",
        fontStyle: "italic",
      },
    },....
  ];
  * - title: string
 */
const splitByPlaceholders = (text: string): string[] => {
  const regex = /{{[^{}]+}}/g;
  return text
    .split(regex)
    .reduce((result, part, index, arr) => {
      result.push(part); // always push the text part
      const match = text.match(regex)?.[index];
      if (match) result.push(match); // interleave placeholder if exists
      return result;
    }, [] as string[])
    .filter((str) => str !== '');
};

const getValueEqual = (key: string) => {
  return `{{${key}}}`;
};

const TextSpecial = ({ params }: TextSpecialParams) => {
  const { key, onPress, style, value } = params;

  const otherProps: TextProps = useMemo(() => {
    return {
      onPress,
    };
  }, [onPress]);

  return (
    <Text
      key={key}
      style={style}
      {...otherProps}
    >
      {value}
    </Text>
  );
};

const formatTextWithParams = ({ text, params }: FormatTextWithParams) => {
  const resultArray = splitByPlaceholders(text);

  return (
    <>
      {resultArray.map((item: string, index: number) => {
        const dataParams = params.find((i) => {
          return item === getValueEqual(i.key);
        });

        if (!isEmpty(dataParams)) {
          return (
            <TextSpecial
              key={`${item}${index}`}
              params={dataParams as IParamsCustomTextMaskDow}
            />
          );
        }

        return <Text key={`${item}${index}`}>{item}</Text>;
      })}
    </>
  );
};

export const CustomMarkDown = ({
  text,
  params,
  containerStyles,
  numberOfLines,
  testID,
}: ICustomTextMarkdown) => {
  return (
    <Text
      testID={testID}
      style={[
        {
          fontFamily: FontFamily.medium,
          color: Colors.BLACK,
          fontSize: FontSizes.SIZE_14,
        },
        containerStyles,
      ]}
      numberOfLines={numberOfLines}
    >
      {formatTextWithParams({ text, params })}
    </Text>
  );
};
