import {
  FlexStyle,
  TouchableOpacityProps as RNTouchableOpacityProps,
  ViewStyle,
} from 'react-native';

import { DefaultStyleProps, SafeAreaInsetType } from '../../utils/stylesProps';

export interface TouchableOpacityProps
  extends DefaultStyleProps,
    RNTouchableOpacityProps {
  /**
   * Width of the component
   */
  width?: number | string;

  /**
   * Height of the component
   */
  height?: number | string;

  /**
   * The flexible items are displayed horizontally, as a row
   */
  row?: boolean;

  /**
   * The flexible items are displayed vertical, as a colum
   */
  column?: boolean;

  /**
   * ```
   * gap <=> { gap: 0}
   * ```
   */
  gap?: number;

  /**
   * ```
   * rowGap <=> { rowGap: 0}
   * ```
   */
  rowGap?: number;

  /**
   * ```
   * columnGap <=> { columnGap: 0}
   * ```
   */
  columnGap?: number;

  /**
   * ```
   * relative <=> { position: 'relative'}
   * ```
   */
  relative?: boolean;

  /**
   * ```
   * absolute <=> { position: 'absolute'}
   * ```
   */
  absolute?: boolean;

  /**
   * ```
   * shadowColor='color' <=> { shadowColor: 'color'}
   * ```
   */
  shadowColor?: string;

  /**
   * ```
   * elevation={number} <=> { elevation: number}
   * ```
   */
  elevation?: number;

  /**
   * ```
   * maxWidth={number} <=> { maxWidth: number}
   * ```
   */
  maxWidth?: number;

  /**
   * ```
   * maxHeight={number} <=> { maxHeight: number}
   * ```
   */
  maxHeight?: number;

  /**
   * ```
   * zIndex={number} <=> { zIndex: number}
   * ```
   */
  zIndex?: number;

  /**
   * Background color of the component - (key of Colors (theme/colors.ts) or Color keywords)
   */
  backgroundColor?: string;

  /**
   * **positionTop** is the number of logical pixels to offset the top edge of this component.
   */
  positionTop?: number | string;

  /**
   * **positionLeft** is the number of logical pixels to offset the top edge of this component.
   */
  positionLeft?: number | string;

  /**
   * **positionRight** is the number of logical pixels to offset the top edge of this component.
   */
  positionRight?: number | string;

  /**
   * **positionBottom** is the number of logical pixels to offset the top edge of this component.
   */
  positionBottom?: number | string;

  /**
   * Render content within the safe area boundaries of a device
   * Example:
   * ```
   * <Block
   *   inset="top"
   * />
   * ```
   * or
   * ```
   * <Block
   *   inset={["top", "bottom"]}
   * />
   * ```
   */
  inset?: SafeAreaInsetType | SafeAreaInsetType[];

  /**
   * Enable default shadow style of component
   * ```
   * {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 3,
    }
   * ```
   */
  shadow?: boolean;

  shadowOffset?: {
    width: number | string;
    height: number | string;
  };

  overflow?: ViewStyle['overflow'];

  justify?: FlexStyle['justifyContent'];

  // old  props
  horizontal?: boolean;
  vertical?: boolean;
  center?: boolean;
  jAround?: boolean;
  jBetween?: boolean;
  right?: boolean;
  left?: boolean;
}
