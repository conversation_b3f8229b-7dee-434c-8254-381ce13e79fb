import React from 'react';
import {
  StyleSheet,
  TouchableOpacity as RNTouchableOpacity,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Shadows } from '../../tokens';
import {
  createDefaultStyle,
  handleGutter,
  handleInset,
  isNumber,
} from '../../utils/stylesProps';
import { styles } from './styles';
import { TouchableOpacityProps as TouchableOpacityPropsType } from './types';

export type TouchableOpacityProps = TouchableOpacityPropsType;

export const TouchableOpacity = ({
  //old props
  horizontal,
  vertical,
  row,
  center,
  right,
  left,
  jAround,
  jBetween,
  //new props
  column,
  gap,
  rowGap,
  columnGap,
  relative,
  absolute,
  shadowColor,
  elevation,
  maxWidth,
  maxHeight,
  zIndex,
  shadow,
  backgroundColor,
  padding,
  margin,
  height,
  width,
  positionTop,
  positionLeft,
  positionRight,
  positionBottom,
  overflow,
  style,
  ...props
}: TouchableOpacityProps) => {
  const insets = useSafeAreaInsets();

  const blockStyles: {} = [
    createDefaultStyle(props),
    maxWidth && { maxWidth },
    maxHeight && { maxHeight },
    width && { width: width },
    height && { height: height },
    row && styles.row,
    column && styles.column,
    shadow && {
      ...Shadows.SHADOW_1,
      shadowColor,
      elevation: elevation || 3,
    },
    gap && { gap },
    rowGap && { rowGap },
    columnGap && { columnGap },
    backgroundColor && { backgroundColor },
    padding && handleGutter('padding', padding),
    margin && handleGutter('margin', margin),
    relative && { position: 'relative' },
    absolute && { position: 'absolute' },
    isNumber(positionTop) && { top: positionTop },
    isNumber(positionLeft) && { left: positionLeft },
    isNumber(positionRight) && { right: positionRight },
    isNumber(positionBottom) && { bottom: positionBottom },
    overflow && { overflow },
    zIndex && { zIndex },
    handleInset(props, insets, padding),

    //old props
    horizontal && { alignItems: 'center' },
    vertical && { justifyContent: 'center' },
    center && { justifyContent: 'center', alignItems: 'center' },
    right && { alignItems: 'flex-end' },
    left && { alignItems: 'flex-start' },
    jAround && { justifyContent: 'space-around' },
    jBetween && { justifyContent: 'space-between' },
    // new props
    { ...StyleSheet.flatten(style) },
  ];

  return (
    <RNTouchableOpacity
      activeOpacity={0.7}
      style={blockStyles}
      {...props}
    />
  );
};
