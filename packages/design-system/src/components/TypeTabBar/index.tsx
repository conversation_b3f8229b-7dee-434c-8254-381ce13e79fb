import React, { useMemo } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';

import { ColorsV2 } from '../../tokens';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { CText } from '../text';
import { styles } from './styles';

type TypeTabBarProps = {
  title: string;
  count?: number;
  onPress?: () => void;
  style?: TouchableOpacityProps['style'];
  isFocused?: boolean;
  testID?: string;
};

export const TypeTabBar = ({
  title,
  count,
  onPress,
  style,
  isFocused,
  testID,
}: TypeTabBarProps) => {
  const styleFocus = useMemo(() => {
    return {
      container: {
        backgroundColor: isFocused ? ColorsV2.orange500 : ColorsV2.neutral50,
      },
      title: {
        color: isFocused ? ColorsV2.neutralWhite : ColorsV2.neutral900,
      },
    };
  }, [isFocused]);

  return (
    <TouchableOpacity
      testID={testID}
      activeOpacity={0.6}
      style={StyleSheet.flatten([
        styles.container,
        styleFocus.container,
        style,
      ])}
      onPress={onPress}
    >
      <BlockView style={styles.txtContainer}>
        <CText
          bold={isFocused}
          numberOfLines={2}
          style={[styles.titleTxt, styleFocus.title]}
        >
          {title}
        </CText>
        <ConditionView
          condition={Boolean(count)}
          viewTrue={
            <BlockView style={styles.badgeContainer}>
              <CText
                bold
                style={styles.badgeTxt}
              >
                {count}
              </CText>
            </BlockView>
          }
        />
      </BlockView>
    </TouchableOpacity>
  );
};
