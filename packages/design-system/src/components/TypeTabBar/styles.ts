import { StyleSheet } from 'react-native';

import { BorderRadius, ColorsV2, FontSizes, Spacing } from '../../tokens';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    borderRadius: BorderRadius.RADIUS_08,
    flex: 1,
    justifyContent: 'center',
    paddingVertical: Spacing.SPACE_08,
    paddingHorizontal: Spacing.SPACE_04,
  },
  titleTxt: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_14,
  },
  txtContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeContainer: {
    width: Spacing.SPACE_16,
    height: Spacing.SPACE_16,
    backgroundColor: ColorsV2.green500,
    borderRadius: BorderRadius.RADIUS_FULL,
    paddingHorizontal: 2,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: -Spacing.SPACE_04,
    right: -Spacing.SPACE_16,
  },
  badgeTxt: {
    color: ColorsV2.neutralWhite,
    fontSize: FontSizes.SIZE_12,
  },
});
