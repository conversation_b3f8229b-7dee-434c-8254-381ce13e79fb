import React, { useEffect, useRef, useState } from 'react';
import {
  AppState,
  AppStateStatus,
  InteractionManager,
  NativeEventSubscription,
} from 'react-native';
import LottieView, { LottieViewProps } from 'lottie-react-native';

export interface LottieProps extends LottieViewProps {
  onAnimationFinish?: () => void;
  count?: number;
}

let listener: NativeEventSubscription;
export const Lottie: React.FC<LottieProps> = ({
  onAnimationFinish,
  count = 1,
  autoPlay,
  loop,
  source,
  style,
}) => {
  const _animation = useRef<LottieView>(null);
  const [appState, setAppState] = useState(AppState.currentState);
  const [countLottie, setCountLottie] = useState(count);

  useEffect(() => {
    //Nếu MODE TESTING thi khong play và call onAnimationFinish nếu có
    // if (ConfigHelpers.isE2ETesting) {
    //   onAnimationFinish?.();
    //   return;
    // }

    InteractionManager.runAfterInteractions((e) => {
      if (!autoPlay) {
        _animation?.current?.play();
      }
    });
    listener = AppState.addEventListener('change', _handleAppStateChange);
    return () => {
      listener?.remove();
    };
  }, []);

  const _handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      _animation?.current?.play();
    }
    setAppState(nextAppState);
  };

  const _onAnimationFinish = () => {
    onAnimationFinish && onAnimationFinish();
    if (count && count > countLottie) {
      _animation?.current?.play();
      setCountLottie(countLottie + 1);
    }
  };
  return (
    <LottieView
      ref={_animation}
      style={style || {}}
      source={source}
      autoPlay={Boolean(autoPlay)}
      loop={Boolean(loop)}
      onAnimationFinish={_onAnimationFinish}
    />
  );
};
