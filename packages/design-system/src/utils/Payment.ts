import { i18n } from '../i18n';
import { useSettingsStore } from '../stores/Settings';
import { Maybe } from '../types';
import {
  PAYMENT_METHOD,
  PAYMENT_METHOD_LIST,
  SERVICES,
  TYPE_OF_PAYMENT,
} from './constant';

export const getPaymentMethods = () => {
  const settingStore = useSettingsStore.getState();
  return settingStore?.settings?.settingSystem?.paymentMethods || {};
};

export const getPaymentMethodsBookTask = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.bookTask] || [];
};

export const getPaymentMethodsSubscription = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.subscription] || [];
};

export const getPaymentMethodsRecharge = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.recharge] || [];
};

export const getPaymentMethodsComboVoucher = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.comboVoucher] || [];
};

export const getPaymentMethodsTopUp = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.topUp] || [];
};

// Get default payment method
export const getDefaultPaymentMethod = (
  options?: Maybe<{
    type?: TYPE_OF_PAYMENT;
    serviceName?: SERVICES;
  }>,
) => {
  const type = options?.type || TYPE_OF_PAYMENT.bookTask;
  const serviceName = options?.serviceName;

  const paymentMethods = getPaymentMethods()[type] || [];
  // paymentMethods.find((item: any) => item.isDefault) || paymentMethods[0]
  let paymentMethod = paymentMethods.find((item: any) => item.name === 'CASH');
  if (
    type === TYPE_OF_PAYMENT.bookTask &&
    (serviceName === SERVICES.HOME_MOVING ||
      serviceName === SERVICES.INDUSTRIAL_CLEANING ||
      serviceName === SERVICES.OFFICE_CLEANING ||
      serviceName === SERVICES.OFFICE_CLEANING_SUBSCRIPTION ||
      serviceName === SERVICES.CLEANING_SUBSCRIPTION)
  ) {
    paymentMethod =
      paymentMethods.find((item: any) => item.name === PAYMENT_METHOD.credit) ||
      paymentMethods[0];
  }
  return getPaymentInfo(paymentMethod.name);
};

export const getPaymentInfo = (paymentMethodName: string) => {
  const paymentMethodSelected = PAYMENT_METHOD_LIST.find(
    (item: any) => item.name === paymentMethodName,
  );
  if (paymentMethodSelected) {
    paymentMethodSelected.label = i18n.t(paymentMethodSelected.label, {
      ns: 'payment',
    });
  }
  return paymentMethodSelected || null;
};
