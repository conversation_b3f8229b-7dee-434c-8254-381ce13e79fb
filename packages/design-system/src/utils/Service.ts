import { SERVICES } from './constant';

export const isCleaningService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.CLEANING);
};

export const isDeepCleaningService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.DEEP_CLEANING);
};

export const isHouseKeepingService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.HOUSE_KEEPING);
};

export const isAirConditionerService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.AIR_CONDITIONER);
};

export const isHomeCookingService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.HOME_COOKING);
};

export const isLaundryService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.LAUNDRY);
};

export const isCleaningServiceSubscription = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.CLEANING_SUBSCRIPTION);
};

export const isGroceryAssistantService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.GROCERY_ASSISTANT);
};

export const isSofaService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.SOFA);
};

export const isElderlyCareService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.ELDERLY_CARE);
};

export const isPatientCareService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.PATIENT_CARE);
};

export const isDisinfectionService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.DISINFECTION_SERVICE);
};

export const isElderlyCareSubService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.ELDERLY_CARE_SUBSCRIPTION);
};

export const isPatientCareSubService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.PATIENT_CARE_SUBSCRIPTION);
};

export const isChildCareService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.CHILD_CARE);
};

export const isChildCareSubService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.CHILD_CARE_SUBSCRIPTION);
};

export const isOfficeCleaningService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.OFFICE_CLEANING);
};

export const isWashingMachineService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.WASHING_MACHINE);
};

export const isWaterHeaterService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.WATER_HEATER);
};

export const isOfficeCleaningSubService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.OFFICE_CLEANING_SUBSCRIPTION);
};

export const isHomeMovingService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.HOME_MOVING);
};

export const isOfficeCarpetCleaningService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.OFFICE_CARPET_CLEANING);
};

export const isMassageService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.MASSAGE);
};

export const isIndustrialCleaning = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.INDUSTRIAL_CLEANING);
};

export const isIroningService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.IRONING);
};

export const isBeautyCareService = (serviceName?: SERVICES) => {
  return Boolean(serviceName === SERVICES.BEAUTY_CARE);
};
