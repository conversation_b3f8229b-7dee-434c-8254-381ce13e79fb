import { useSettingsStore } from '../stores/Settings';
import { PAYMENT_METHOD_LIST, TYPE_OF_PAYMENT } from './constant';

export const getPaymentMethods = () => {
  const settingStore = useSettingsStore.getState();
  return settingStore?.settings?.settingSystem?.paymentMethods || {};
};

export const getPaymentMethodsBookTask = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.bookTask] || [];
};

export const getPaymentMethodsSubscription = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.subscription] || [];
};

export const getPaymentMethodsRecharge = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.recharge] || [];
};

export const getPaymentMethodsComboVoucher = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.comboVoucher] || [];
};

export const getPaymentMethodsTopUp = () => {
  const paymentMethodsStore = getPaymentMethods();
  return paymentMethodsStore[TYPE_OF_PAYMENT.topUp] || [];
};

// Get default payment method
export const getDefaultPaymentMethod = (
  type: TYPE_OF_PAYMENT = TYPE_OF_PAYMENT.bookTask,
) => {
  const paymentMethods = getPaymentMethods()[type] || [];
  return (
    paymentMethods.find((item: any) => item.isDefault) || paymentMethods[0]
  );
};

export const getPaymentInfo = (paymentMethodName: string, t: any) => {
  const paymentMethod = PAYMENT_METHOD_LIST.find(
    (item: any) => item.name === paymentMethodName,
  );
  if (t && paymentMethod) {
    paymentMethod.label = t(paymentMethod?.label);
  }
  return paymentMethod || null;
};
