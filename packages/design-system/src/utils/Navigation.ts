/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-02-14 15:31
 * @modify date 2024-10-06 16:05:43
 * @desc <PERSON><PERSON> sử dụng navigation ở bất kỳ component nào mà không cần truyền props.navigation
 */

import * as React from 'react';
import { CommonActions, StackActions } from '@react-navigation/native';

export const navigationRef = React.createRef();

type NavigationParams = Record<string, any>;

export class NavigationService {
  static async navigateTo(name: string, params?: NavigationParams) {
    this.popToTop();
    navigationRef?.current?.navigate(name, params);
  }

  static async navigate(name: string, params?: NavigationParams) {
    navigationRef?.current?.navigate(name, params);
  }

  static popToTop() {
    if (this.canGoBack()) {
      navigationRef?.current?.dispatch(StackActions.popToTop());
    }
  }

  static canGoBack() {
    return navigationRef?.current?.canGoBack();
  }

  /**
   * @description Xóa list screen ra khỏi stack hiện tại
   * @param listScreenNameRemove string[] list screen muốn xóa ra khỏi stack
   * return example:
   * stack = ["a", "b", "c", "d"] (stack hiện tại)
   * Muốn remove screen "b" và "c" ra khỏi stack
   * removeScreenFromStack(["b", "c"])
   * newStack = ["a", "d"]
   */
  static removeScreenFromStack(listScreenNameRemove: string[], tabIndex = 0) {
    if (listScreenNameRemove?.length) {
      navigationRef?.current?.dispatch((state: any) => {
        const routes = [
          ...state.routes.filter(
            (route: any) => !listScreenNameRemove.includes(route.name),
          ),
        ];
        const index = state?.type === 'tab' ? tabIndex || 0 : routes.length - 1;
        return CommonActions.reset({
          ...state,
          routes,
          index,
        });
      });
    }
  }

  static goBack() {
    return navigationRef?.current?.goBack();
  }
}
