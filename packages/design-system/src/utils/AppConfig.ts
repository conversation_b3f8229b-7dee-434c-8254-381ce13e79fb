import * as Keychain from 'react-native-keychain';

import { Maybe } from '../types';
import { undefinedValue } from './constant';

type IKeyEnv = {
  VN: string;
  TH: string;
  ID: string;
  MY: string;
};

type IAws3 = {
  keyPrefix: string;
  bucket: string;
  region: string;
  secretKey: string;
  accessKey: string;
};

type IADYEN = {
  key: string;
  libraryLocation: string;
};

type IMidtrans = {
  key: string;
  libraryLocation: string;
};

type I2C2P = {
  libraryLocation: string;
};

type IVNPay = {
  tmnCode: string;
};

type ILoginGoogle = {
  signInKeyIOS: string;
  signInKeyAndroid: string;
};

type IRemoteConfig = {
  BASE_URL?: string;
  DECRYPT_KEY?: string;
  ACCESS_KEY_ENV?: IKeyEnv;
};

type IKeyConfig = {
  ACCESS_KEY?: string;
  SECRET_KEY?: string;
  AWS3?: Maybe<IAws3>;
  ADYEN?: Maybe<IADYEN>;
  WEB_SOCKET_ENDPOINT?: string;
  API_MAINTAIN_URL?: string;
  API_SERVER_URL?: string;
  MIDTRANS?: Maybe<IMidtrans>;
  VNPAY?: Maybe<IVNPay>;
  CONFIG_2C2P?: Maybe<I2C2P>;
  LOGIN_GOOGLE?: Maybe<ILoginGoogle>;
};

type IAppConfig = IRemoteConfig & IKeyConfig;

const NAME = 'remote_config';
const KEYCHAIN_KEY = 'btakee_config';

function filterNonEmptyFields<T extends Record<string, any>>(
  obj: T,
): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([key, value]) => {
      return value !== null && value !== undefinedValue && value !== '';
    }),
  ) as Partial<T>;
}

export class AppConfig {
  private static config: Maybe<IAppConfig> = null;

  private static set(config: Maybe<IAppConfig>) {
    this.config = config;
  }

  static get() {
    return this.config;
  }

  static async getFromStorage() {
    try {
      // Retrieve the credentials
      const credentials = await Keychain.getGenericPassword({
        service: KEYCHAIN_KEY,
      });
      if (credentials) {
        const parsedData: IAppConfig = JSON.parse(credentials.password);
        this.set(parsedData);
        return parsedData;
      }
    } catch (error) {
      console.error('Failed to access Keychain', error);
    }
  }

  static async saveToStorage(data: IAppConfig) {
    // Lọc các field không rỗng
    const filteredData = filterNonEmptyFields(data);
    const newData = {
      ...this.config,
      ...filteredData,
    };

    this.set(newData);
    await Keychain.setGenericPassword(NAME, JSON.stringify(newData), {
      service: KEYCHAIN_KEY,
    });
  }

  static async resetConfig() {
    this.set(null);
    await Keychain.resetGenericPassword();
  }
}
