import { useEffect, useRef } from 'react';
import {
  useQuery,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import { IApiError } from '../types';
import { apiRequest, createCancelToken } from './api-request';
import { EndpointKeys } from './endpoints';
import { ApiType } from './types/index';

export const queryKeys = {
  user: {
    profile: (userId: string) => ['user', 'profile', userId],
    all: () => ['user', 'all'],
  },
  tasks: {
    detail: (taskId: string) => ['tasks', 'detail', taskId],
    upcoming: (userId?: string) => ['tasks', 'upcoming', userId],
    schedule: (userId?: string) => ['tasks', 'schedule', userId],
    monthly: (userId?: string) => ['tasks', 'monthly', userId],
    history: (userId?: string) => ['tasks', 'history', userId],
  },
  // Add more query keys as needed
};

/**
 * Custom hook for API queries using react-query
 * @param endpoint - API endpoint key
 * @param params - Request parameters
 * @param queryKey - Query key for caching
 * @param isoCode - ISO country code (optional)
 * @param options - Additional query options
 */
export function useApiQuery<T extends EndpointKeys>({
  key,
  params,
  queryKey,
  options,
}: {
  key: T;
  queryKey: unknown[];
  params?: ApiType[T]['params'];
  options?: Omit<
    UseQueryOptions<
      ApiType[T]['response'],
      IApiError,
      ApiType[T]['response'],
      unknown[]
    >,
    'queryKey' | 'queryFn'
  >;
}): UseQueryResult<ApiType[T]['response'], IApiError> {
  // Create a ref for the cancel token to persist across renders
  const cancelTokenRef = useRef(createCancelToken());

  // Reset cancel token on unmount
  useEffect(() => {
    return () => {
      cancelTokenRef.current.cancel('Component unmounted');
    };
  }, []);

  return useQuery<
    ApiType[T]['response'],
    IApiError,
    ApiType[T]['response'],
    unknown[]
  >({
    queryKey,
    queryFn: () => {
      return apiRequest({
        key,
        params,
        cancelToken: cancelTokenRef.current,
      });
    },
    ...options,
  });
}
