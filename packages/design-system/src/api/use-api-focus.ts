import { useCallback, useEffect, useRef } from 'react';
import {
  useQuery,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import { IApiError } from '../types';
import { apiRequest, createCancelToken } from './api-request';
import { EndpointKeys } from './endpoints';
import { ApiType } from './types/index';

/**
 * @fileoverview Enhanced useApiQuery hook for React Native with TypeScript
 *
 * This hook provides type-safe API consumption with React Query integration,
 * optimized for React Native performance and senior-level development patterns.
 *
 * ## React Query Caching Strategy Explained
 *
 * React Query uses multiple cache-related concepts:
 *
 * 1. **staleTime**: How long data is considered "fresh" (won't refetch)
 * 2. **gcTime** (formerly cacheTime): How long data stays in cache after being unused
 * 3. **refetchOnWindowFocus**: Whether to refetch when app/screen comes into focus
 * 4. **refetchOnMount**: Whether to refetch when component mounts
 *
 * ### Cache Scenarios:
 *
 * **Scenario 1: Always Fresh Data on Focus**
 * ```tsx
 * const { data } = useApiQuery({
 *   key: EndpointKeys.getUpComingTasks,
 *   queryKey: ['tasks', 'upcoming'],
 *   options: {
 *     staleTime: 0, // Data is always considered stale
 *     refetchOnWindowFocus: true, // Refetch when screen/app focused
 *     refetchOnMount: true, // Refetch when component mounts
 *   }
 * });
 * ```
 *
 * **Scenario 2: Smart Caching with Background Updates**
 * ```tsx
 * const { data } = useApiQuery({
 *   key: EndpointKeys.getUserProfile,
 *   queryKey: ['user', userId],
 *   options: {
 *     staleTime: 5 * 60 * 1000, // Consider fresh for 5 minutes
 *     refetchOnWindowFocus: true, // But still check on focus
 *     refetchInterval: 2 * 60 * 1000, // Auto-refresh every 2 minutes
 *   }
 * });
 * ```
 *
 * **Scenario 3: Aggressive Caching for Static Data**
 * ```tsx
 * const { data } = useApiQuery({
 *   key: EndpointKeys.getSettings,
 *   queryKey: ['settings'],
 *   options: {
 *     staleTime: 60 * 60 * 1000, // Fresh for 1 hour
 *     gcTime: 2 * 60 * 60 * 1000, // Keep in cache for 2 hours
 *     refetchOnWindowFocus: false, // Don't refetch on focus
 *   }
 * });
 * ```
 *
 * ### Your Use Case: Fresh Data on Page Focus
 *
 * For your requirement (always newest data when page focuses), use:
 *
 * ```tsx
 * function TaskScreen() {
 *   const { data: tasks, isLoading, refetch } = useApiQuery({
 *     key: EndpointKeys.getUpComingTasks,
 *     queryKey: ['tasks', 'upcoming'],
 *     options: {
 *       // Option 1: Always refetch on focus (most reliable)
 *       staleTime: 0,
 *       refetchOnWindowFocus: true,
 *
 *       // Option 2: Use React Navigation focus event
 *       enabled: true,
 *     }
 *   });
 *
 *   // Option 3: Manual refetch on screen focus
 *   useFocusEffect(
 *     useCallback(() => {
 *       refetch();
 *     }, [refetch])
 *   );
 *
 *   return <TaskList tasks={tasks} />;
 * }
 * ```
 *
 * ### Performance Considerations:
 *
 * - **Network requests**: More frequent requests = more battery/data usage
 * - **UI updates**: Frequent refetches can cause loading states
 * - **Balance**: Use `refetchOnWindowFocus: true` with reasonable `staleTime`
 *
 * @example Basic Usage
 * ```tsx
 * import { useApiQuery, EndpointKeys } from '@/api';
 *
 * function UserProfile({ userId }: { userId: string }) {
 *   const { data, isLoading, error, refetch } = useApiQuery({
 *     key: EndpointKeys.getUser,
 *     queryKey: ['user', userId],
 *     params: { userId },
 *     options: {
 *       enabled: !!userId,
 *       staleTime: 5 * 60 * 1000, // 5 minutes
 *       retry: 2,
 *     }
 *   });
 *
 *   if (isLoading) return <LoadingSpinner />;
 *   if (error) return <ErrorMessage error={error} />;
 *   if (!data) return <EmptyState />;
 *
 *   return <UserProfileComponent user={data} />;
 * }
 * ```
 *
 * @example Advanced Usage with Conditional Query
 * ```tsx
 * import { useApiQuery, createQueryKey, EndpointKeys } from '@/api';
 *
 * function TaskList({ userId, serviceId }: { userId: string; serviceId?: string }) {
 *   const { data: tasks, isLoading } = useApiQuery({
 *     key: EndpointKeys.getUpComingTasks,
 *     queryKey: createQueryKey.tasks.upcoming(userId),
 *     params: { userId, serviceId },
 *     options: {
 *       enabled: !!userId,
 *       staleTime: 2 * 60 * 1000, // 2 minutes for dynamic data
 *       refetchOnWindowFocus: true,
 *       refetchInterval: 30 * 1000, // Auto-refresh every 30 seconds
 *     }
 *   });
 *
 *   return (
 *     <FlatList
 *       data={tasks}
 *       renderItem={({ item }) => <TaskItem task={item} />}
 *       refreshing={isLoading}
 *       onRefresh={() => refetch()}
 *     />
 *   );
 * }
 * ```
 *
 * @example Error Handling with Custom Logic
 * ```tsx
 * function PriceCalculator({ serviceData }: { serviceData: ServiceData }) {
 *   const { data: pricing, error, isError } = useApiQuery({
 *     key: EndpointKeys.getPriceCleaning,
 *     queryKey: ['pricing', 'cleaning', serviceData.id],
 *     params: { serviceData },
 *     options: {
 *       retry: (failureCount, error) => {
 *         // Custom retry logic
 *         if (error.code === 422) return false; // Don't retry validation errors
 *         return failureCount < 3;
 *       },
 *       onError: (error) => {
 *         // Custom error handling
 *         if (error.code === 401) {
 *           // Handle auth error
 *           NavigationService.navigate('Login');
 *         }
 *       }
 *     }
 *   });
 *
 *   if (isError && error.code === 422) {
 *     return <ValidationErrorMessage errors={error.data} />;
 *   }
 *
 *   return <PricingDisplay pricing={pricing} />;
 * }
 * ```
 */

// Type helper to ensure T is a valid key of ApiType
type ValidEndpointKey<T> = T extends keyof ApiType ? T : never;

// Type helper to get params type, handling both optional and required params
type GetParamsType<T extends keyof ApiType> = ApiType[T] extends {
  params: infer P;
}
  ? P extends undefined
    ? never
    : P
  : never;

// Type helper to get response type
type GetResponseType<T extends keyof ApiType> = ApiType[T] extends {
  response: infer R;
}
  ? R
  : never;

// Interface for useApiQuery parameters
interface UseApiQueryParams<T extends keyof ApiType> {
  key: ValidEndpointKey<T>;
  queryKey: unknown[];
  params?: GetParamsType<T>;
  options?: Omit<
    UseQueryOptions<
      GetResponseType<T>,
      IApiError,
      GetResponseType<T>,
      unknown[]
    >,
    'queryKey' | 'queryFn'
  >;
}

/**
 * Custom hook for API queries using React Query with proper TypeScript support
 *
 * Features:
 * - Type-safe API endpoint consumption
 * - Automatic request cancellation on unmount
 * - Proper error handling with custom error types
 * - Optimized for React Native performance
 *
 * @example
 * ```tsx
 * const { data, isLoading, error } = useApiQuery({
 *   key: EndpointKeys.getUser,
 *   queryKey: ['user', userId],
 *   params: { userId },
 *   options: {
 *     enabled: !!userId,
 *     staleTime: 5 * 60 * 1000, // 5 minutes
 *   }
 * });
 * ```
 */
export function useApiFocus<T extends keyof ApiType>({
  key,
  params,
  queryKey,
  options,
}: UseApiQueryParams<T>): UseQueryResult<GetResponseType<T>, IApiError> {
  // Create a ref for the cancel token to persist across renders
  const cancelTokenRef = useRef(createCancelToken());

  // Memoize the query function to prevent unnecessary re-renders
  const queryFn = useCallback((): Promise<GetResponseType<T>> => {
    // Reset cancel token for new request
    cancelTokenRef.current = createCancelToken();

    return apiRequest({
      key: key as EndpointKeys,
      params: params as any, // Type assertion needed due to conditional types
      cancelToken: cancelTokenRef.current,
    }) as Promise<GetResponseType<T>>;
  }, [key, params]);

  // Clean up cancel token on unmount or key changes
  useEffect(() => {
    return () => {
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel(
          'Component unmounted or dependencies changed',
        );
      }
    };
  }, [key]);

  return useQuery<GetResponseType<T>, IApiError, GetResponseType<T>, unknown[]>(
    {
      queryKey,
      queryFn,
      // Default options for React Native optimization
      staleTime: 30 * 1000, // 30 seconds
      gcTime: 5 * 60 * 1000, // 5 minutes (renamed from cacheTime in v5)
      retry: (failureCount, error) => {
        // Don't retry on auth errors or client errors
        if (error?.code === 401 || error?.code === 403 || error?.code === 400) {
          return false;
        }
        // Retry up to 2 times for network errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Merge with user-provided options
      ...options,
    },
  );
}

// Export helper function to create query keys consistently
export const createQueryKey = {
  user: {
    profile: (userId: string) => ['user', 'profile', userId] as const,
    all: () => ['user', 'all'] as const,
  },
  tasks: {
    detail: (taskId: string) => ['tasks', 'detail', taskId] as const,
    upcoming: (userId?: string) => ['tasks', 'upcoming', userId] as const,
    schedule: (userId?: string) => ['tasks', 'schedule', userId] as const,
    monthly: (userId?: string) => ['tasks', 'monthly', userId] as const,
  },
  // Add more query key factories as needed
} as const;

// Export type helpers for external use
export type { GetParamsType, GetResponseType, ValidEndpointKey };

/**
 * Hook optimized for screens that need fresh data on focus
 * Perfect for task lists, notifications, or any real-time data
 *
 * @example
 * ```tsx
 * import { useFreshApiFocus } from '@/api';
 *
 * function TaskScreen() {
 *   const { data: tasks, isLoading, refetch } = useFreshApiFocus({
 *     key: EndpointKeys.getUpComingTasks,
 *     queryKey: ['tasks', 'upcoming'],
 *     params: { userId: 'user123' }
 *   });
 *
 *   return (
 *     <FlatList
 *       data={tasks}
 *       renderItem={({ item }) => <TaskItem task={item} />}
 *       refreshing={isLoading}
 *       onRefresh={refetch}
 *     />
 *   );
 * }
 * ```
 */
export function useFreshApiFocus<T extends keyof ApiType>(
  params: Omit<UseApiQueryParams<T>, 'options'> & {
    options?: Partial<UseApiQueryParams<T>['options']>;
  },
): UseQueryResult<GetResponseType<T>, IApiError> {
  return useApiFocus({
    ...params,
    options: {
      // Always consider data stale, so it refetches on focus
      staleTime: 0,
      // Refetch when screen/app comes into focus
      refetchOnWindowFocus: true,
      // Refetch when component mounts
      refetchOnMount: true,
      // Don't retry too aggressively for real-time data
      retry: 1,
      // Merge with any custom options
      ...params.options,
    },
  });
}
