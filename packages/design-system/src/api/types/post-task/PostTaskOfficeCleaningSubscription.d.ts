import { IAddons,IDetailAreaOfficeCleaning } from '../../../types';

export type IDataBookingOfficeCleaningSubscription = {
  startDate?: string; // ISO string
  endDate?: string; // ISO string
  timezone?: string;
  taskPlace?: {
    country?: string;
    city?: string;
    district?: string;
  };
  weekday?: number[]; // 0 = Sunday, 1 = Monday, etc.
  duration?: number; // in hours or sessions
  serviceId?: string;
  homeType?: string; // You can narrow this down if needed
  payment?: {
    method?: string;
  };
  address?: string;
  contactName?: string;
  location?: {
    lat?: number;
    lng?: number;
  };
  countryCode?: string; // e.g. "+84"
  description?: string;
  deviceInfo?: {
    systemVersion?: string;
    appVersion?: string;
    buildNumber?: string;
    brand?: string;
    model?: string;
    timezone?: string;
    isTablet?: boolean;
    baseOs?: string;
  };
  houseNumber?: string;
  taskNote?: string;
  isoCode?: string;
  shortAddress?: string;
  month?: number; // Not clear if this is used elsewhere
  schedule?: string[]; // ISO date strings
  isPremium?: boolean;
  addons?: IAddons[];
  pet?: {
    name?: string;
    other?: string;
  }[];
  shopperIP?: string;
  detailOfficeCleaning?: IDetailAreaOfficeCleaning;
};

export type IResponsePostTaskOfficeCleaningSubscription = {
  _id?: string;
  orderId?: string;
  phone?: string;
  taskPlace?: {
    city?: string;
    country?: string;
    district?: string;
  };
};

export class IPostTaskOfficeCleaningSubscriptionAPI {
  params?: IDataBookingOfficeCleaningSubscription;
  response?: IResponsePostTaskOfficeCleaningSubscription;
  error?: any;
}
