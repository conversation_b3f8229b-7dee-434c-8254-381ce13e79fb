import { IOptionAreaOfficeCleaning } from '../../../types';
import { IDataBookingCleaning } from './PostTaskCleaning';

export type IDataBookingOfficeCleaning = {
  detailOfficeCleaning?: IOptionAreaOfficeCleaning;
} & IDataBookingCleaning;

export class IPostTaskOfficeCleaningAPI {
  params?: IDataBookingOfficeCleaning;
  response?: {
    bookingId?: string;
    isPrepayment?: boolean;
  };
  error?: any;
}
