import { IDate, ITimezone } from '../../../helpers';
import { IDetailAreaOfficeCleaning, IHomeType, IPrice } from '../../../types';

export class IParamsGetPriceOfficeCleaning {
  task: {
    timezone: ITimezone;
    date: IDate;
    autoChooseTasker: boolean;
    taskPlace: {
      country: string;
      city: string;
      district: string;
    };
    homeType: IHomeType;
    duration: number;
    payment: {
      method: string;
    };
    detailOfficeCleaning: IDetailAreaOfficeCleaning;
  };
  service: {
    _id: string;
  };
  isoCode: string;
}

export class IGetPriceOfficeCleaningAPI {
  params?: IParamsGetPriceOfficeCleaning;
  response?: IPrice;
  error?: any;
}
