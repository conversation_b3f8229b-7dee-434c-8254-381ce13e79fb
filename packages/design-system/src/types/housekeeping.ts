import { ISO_CODE } from '../utils';
import { IObjectText } from './global';

export enum NameHostelType {
  Apartment = 'APARTMENT',
  Home = 'HOME',
  Villa = 'VILLA',
  Hotel = 'HOTEL',
  Office_building = 'OFFICE_BUILDING',
}

export type IHostelType = {
  name?: NameHostelType;
  text?: IObjectText;
  icon?: string;
  roomTypes?: IRoomType[];
  options?: IHouseKeepingOption[];
  surcharge?: ISurCharge[];
};

export type IRoomType = {
  name?: string;
  text?: IObjectText;
  description?: IObjectText;
  price?: number;
  duration?: number; // theo giờ
  quantity?: number;
};

export type IHouseKeepingOption = {
  name?: string;
  text?: IObjectText;
  description?: IObjectText;
  duration?: number;
  icon?: string;
  quantity?: number;
};

export type ISurCharge = {
  name?: string;
  text?: IObjectText;
  icon?: string;
  price?: number;
  min?: number;
  max?: number;
};

export type IListRoomImage = {
  name?: string;
  images?: string[];
};

export type IDetailHousekeeping = {
  name?: string;
  options?: IHouseKeepingOption[];
  roomNumber?: string;
  roomTypes: IRoomTypeDetail[];
  text?: IObjectText;
};
export type IRoomTypeDetail = Pick<
  IRoomType,
  'name' | 'description' | 'text' | 'quantity'
> & {
  setupImages: string[];
};

export type IHostelDetail = {
  hostelId?: string;
  hostelName?: string;
  rooms?: IHostelRoom[];
  totalArea?: number;
};
export type IHostelRoom = {
  area?: number;
  id?: string;
  images?: string[];
  name: string;
  note?: string;
  quantity?: number;
};
export type IHousekeepingLocation = {
  _id?: string;
  address?: string;
  city?: string;
  contact?: string;
  country?: ISO_CODE;
  countryCode?: string;
  description?: string;
  district?: string;
  homeType?: string;
  isoCode?: ISO_CODE;
  lat?: number;
  lng?: number;
  phoneNumber?: string;
  shortAddress?: string;
  locationName?: string;
  userId?: string;
};
