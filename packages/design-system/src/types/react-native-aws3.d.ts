declare module 'react-native-aws3' {
  export interface File {
    uri: string;
    name: string;
    type: string;
  }

  export interface Options {
    keyPrefix: string;
    bucket: string;
    region: string;
    accessKey: string;
    secretKey: string;
    successActionStatus: number;
  }

  export interface Response {
    status: number;
    body: {
      postResponse: {
        location: string;
      };
    };
  }

  export interface RNS3 {
    put(file: File, options: Options): Promise<Response>;
  }

  export const RNS3: RNS3;
}
