import { IObjectText, Maybe } from './global';
// Massage-specific types
export enum TypeNumberOfPeopleMassage {
  Single = 'SINGLE',
  Couple = 'COUPLE',
}

export enum MassagePostTaskType {
  Step1 = 1,
  Step2 = 2,
  Step3 = 3,
  Step4 = 4,
}

export enum GenderMassage {
  Male = 'MALE',
  Female = 'FEMALE',
  Random = 'RANDOM',
  Both = 'BOTH',
}

export type IPackageMassageItem = {
  name?: string;
  imageUrl?: string;
  thumbnail?: string;
  title?: any; // IObjectText
  content?: any; // IObjectText
  options?: IOptionItem[];
  overview?: any;
  benefit?: any;
  accessories?: any;
  workingProcess?: any;
  isShowButtonDuplicateForPartner?: boolean;
};

export type IOptionItem = {
  name?: string;
  text?: any; // IObjectText
  price?: number;
  duration?: number;
};

export type IPackageOption = {
  name?: string;
  title?: any; // IObjectText
  thumbnail?: string;
  taskerGender?: Maybe<GenderMassage>;
  option?: {
    name?: string;
    text?: any; // IObjectText
  };
};

export enum TypeExecutionOrder {
  Sequence = 'SEQUENCE',
  Simultaneously = 'SIMULTANEOUSLY',
}

export type IMassageDetailService = {
  accessories?: {
    image?: string;
    text?: IObjectText;
    description?: IObjectText;
  }[];
  city?: {
    name?: string;
    packages?: IPackageMassageItem[];
  };
  introService?: {
    title?: IObjectText;
    description?: IObjectText;
    content: {
      image?: string;
      description?: IObjectText;
      subContent?: IObjectText[];
    }[];
  }[];
  benefit?: {
    [key: string]: IObjectText[];
  };
  overview?: {
    [key: string]: string[];
  };
  workingProcess?: {
    [key: string]: {
      description?: IObjectText;
      title?: IObjectText;
    }[];
  };
};
