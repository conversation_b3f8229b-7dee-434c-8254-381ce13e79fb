import { IDate } from '../helpers/date-time.helpers';
import { CountryCode, ISO_CODE } from '../utils/constant';
import { IDeviceInfo, ILocation } from './global';
import { IAddons, IService } from './service';
import { IUser } from './user';

export type IAddress = {
  _id?: string;
  address?: string;
  city?: string;
  contact?: string;
  country?: ISO_CODE;
  countryCode?: CountryCode;
  description?: string;
  district?: string;
  homeType?: string;
  lat?: number;
  lng?: number;
  phoneNumber?: string;
  shortAddress?: string;
  isAddressMaybeWrong?: boolean;
};

export type IRelatedTask = {
  relatedTaskId?: string;
  service?: IService;
  bookingId?: string;
  date?: IDate;
  address?: IAddress;
  limitDate?: number;
  servicesPosted?: IService[];
};

export type IParamsCheckTaskSameTime = {
  taskDate: string;
  serviceId: string;
};

export type IDataBooking = {
  address?: string;
  locations?: ILocation[];
  user?: IUser;
  service?: IService;
  homeNumber?: string;
  homeType?: string;
  finalCost?: {
    amount: number;
  };
  // Additional properties
  contactName?: string;
  lat?: number;
  lng?: number;
  phone?: string;
  countryCode?: string;
  description?: string;
  askerId?: string;
  autoChooseTasker?: boolean;
  date?: string;
  timezone?: string;
  deviceInfo?: IDeviceInfo;
  duration?: number;
  houseNumber?: string;
  isSendToFavTaskers?: boolean;
  isoCode?: string;
  payment?: any;
  serviceId?: string;
  taskPlace: {
    city?: string;
    country?: string;
    district?: string;
    isAddressMaybeWrong?: boolean;
  };
  updateTaskNoteToUser?: boolean;
  shortAddress?: string;
  isTetBooking?: boolean;
  taskNote?: string;
  requirements?: {
    type: number;
  }[];
  pet?: any;
  promotion?: any;
  isPremium?: boolean;
  gender?: string;
  forceTasker?: {
    taskerId?: string;
    isResent?: boolean;
  };
  dateOptions?: IDate[];
  addons?: IAddons[];
  source?: {
    from?: string;
    taskId?: string;
  };
  weekday?: number[];
};
