import { ITimezone } from '../helpers';
import { ISO_CODE, PAYMENT_METHOD, SERVICES, TASK_STATUS } from '../utils';
import { IObjectText } from './global';
import { IService, ITaskPlace } from './service';

export type IDateOptionItem = { eatingTime?: string } & Pick<
  ITaskDetail,
  '_id' | 'date' | 'costDetail'
>;

export enum STATUS_OF_PREPAYMENT {
  new = 'NEW',
  paid = 'PAID',
  error = 'ERROR',
  canceled = 'CANCELED',
  charging = 'CHARGING',
}

export type IAcceptedTasker = {
  avatar?: string;
  isPremiumTasker?: boolean;
  name?: string;
  taskerId?: string;
  phone?: string;
};

export type ICostDetail = {
  baseCost?: number;
  cost?: number;
  currency: {
    code?: string;
    sign?: string;
  };
  depositMoney?: number;
  duration?: number;
  finalCost?: number;
  newFinalCost?: number;
  totalCost?: number;
  transportFee?: number;
  vat?: number;
};

type ICurrency = {
  code?: string;
  sign?: string;
};

export type IRequirement = {
  type?: number;
  text?: IObjectText;
  icon?: string;
  duration?: number;
  cost?: number;
};

export type ITaskDetail = {
  _id?: string;
  isoCode?: ISO_CODE;
  duration?: number;
  date?: string;
  collectionDate?: Date;
  taskNote?: string;
  acceptedTasker?: IAcceptedTasker[];
  status?: TASK_STATUS;
  service?: IService;
  servicesPosted?: IService[];
  isPrepayTask?: boolean;
  serviceName?: SERVICES;
  address?: string;
  shortAddress?: string;
  costDetail?: ICostDetail;
  forceTasker?: IAcceptedTasker;
  dateOptions?: IDateOptionItem[];
  description?: string;
  contactName?: string;
  countryCode?: string;
  phone?: string;
  originCurrency?: ICurrency;
  payment?: {
    method?: PAYMENT_METHOD;
    status?: STATUS_OF_PREPAYMENT;
  };
  timezone?: ITimezone;
  requirements?: IRequirement[];
  taskPlace?: ITaskPlace;
  pet?: {
    name?: string;
    other?: string;
  }[];
  isPremium?: boolean;
  weekdays?: number[];
  outStandingPayment?: any;
  isTetBooking?: boolean;
  weeklyRepeater?: number[];
  schedule?: string[];
  detailChildCare?: any;
  disinfectionDetail?: any;
  detailIndustrialCleaning?: any;
  detailCarpet?: {
    customArea?: number;
    area?: {
      from?: number;
      to?: number;
    };
  };
  detailOfficeCleaning?: {
    numberOfTaskers?: number;
    area?: string;
  };
  isEco?: boolean;
};
