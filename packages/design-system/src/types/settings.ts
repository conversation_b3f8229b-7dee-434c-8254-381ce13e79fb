import { SERVICES } from '../utils';
import { ICurrency, IObjectText, Maybe } from './global';
import { IService } from './service';

type IServiceGroup = {
  _id?: string;
  name?: string;
  text?: IObjectText;
  services?: {
    _id?: string;
    name?: SERVICES;
    text?: IObjectText;
    icon?: string;
    isNewService?: boolean;
  }[];
};

export type ISettings = {
  askerSetting?: any; // TODO: define type
  communitySetting?: any; // TODO: define type
  currency?: ICurrency;
  serviceGroup?: IServiceGroup[];
  services?: IService[];
  settingSystem?: any; // TODO: define type
  subscriptionSetting?: any; // TODO: define type
};
