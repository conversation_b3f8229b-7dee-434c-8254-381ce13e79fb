import { IObjectText } from './global';

export type IOptionAreaOfficeCleaning = {
  area: number;
  numberOfTaskers: number;
  duration: number;
};

export type IDetailAreaOfficeCleaning = {
  name: string;
  text: IObjectText;
  options: IOptionAreaOfficeCleaning[];
};

export type IDetailOfficeCleaning = {
  areas: IDetailAreaOfficeCleaning[];
};

export type IVatInfo = {
  companyAddress: string;
  companyEmail: string;
  companyName: string;
  taxCode: string;
};
