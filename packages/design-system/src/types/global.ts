export interface ILocation {
  lat: number;
  lng: number;
  country: string;
  city: string;
  district: string;
  address: string;
  contact?: string;
  phoneNumber?: string;
  shortAddress: string;
  countryCode?: string;
  isAddressMaybeWrong?: boolean;
  description?: string;
  homeType?: string;
}

export interface IDeviceInfo {
  systemVersion?: string;
  appVersion?: string;
  buildNumber?: string;
  brand?: string;
  model?: string;
  timezone?: string;
  isTablet?: boolean;
  baseOs?: string;
}

export type ICity = {
  key?: string;
  name?: string;
  status?: string;
  timezone?: string;
  district?: IDistrict[];
};

export type IDistrict = {
  key?: string;
  name?: string;
};

export type Maybe<T> = T | null | undefined;

export interface IObjectText {
  vi: string;
  en: string;
  ko?: string;
  th?: string;
  id?: string;
  ms?: string;
}

export type ICurrency = {
  code?: string;
  sign?: string;
};

export type IImage = {
  uri: string;
  name: string;
  _id?: string;
  type: string;
  link?: string;
  oldName?: string;
};

export enum GalleryType {
  video = 'video/mp4',
  image = 'image/png',
}

export type IGallery = {
  uri: string;
  name: string;
  type: GalleryType;
  width?: number;
  height?: number;
  link?: Maybe<string>;
  size?: Maybe<number>;
  duration?: number;
};
