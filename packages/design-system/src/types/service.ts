import { REQUIREMENTS_TYPE, SERVICES } from '../utils';
import { IHostelType } from './housekeeping';
import { IHomeMoving, IMassageDetailService, IObjectText } from './index';
import { IDetailOfficeCleaning } from './office-cleaning';

export interface Addon {
  cost: number;
  name: string;
  text: IObjectText;
}

export type ITaskPlace = {
  country?: string;
  city?: string;
  district?: string;
};

export interface RelatedService {
  _id: string;
  name: string;
  text: IObjectText;
  thumbnail: string;
}

export interface Requirement {
  duration?: number;
  icon: string;
  text: IObjectText;
  type: REQUIREMENTS_TYPE;
  cost?: number;
  costByDuration?: {
    duration: number;
    cost: number;
  }[];
}

export interface WorkingProcessDetail {
  name: string;
  text: IObjectText;
  workToDo: IObjectText[];
}

export interface WorkingProcessV2 {
  detail: WorkingProcessDetail[];
}

export enum IStatusEcoOption {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export type IMonthlyOption = {
  duration: number;
  name: string;
  text: IObjectText;
};

export interface IService {
  _id: string;
  addons: Addon[];
  city: string[];
  defaultTaskTime: number;
  icon: string;
  name: SERVICES;
  optional: {
    isAutoChooseTaskerEnabled: boolean;
  };
  postingLimits: {
    from: string;
    to: string;
  };
  premiumOptions: {
    applyForCities: string[];
    status: string;
  };
  priceSetting: {
    costForChooseTasker: number;
  };
  relatedServices: RelatedService[];
  requirements: Requirement[];
  shortText: IObjectText;
  status: string;
  text: IObjectText;
  thumbnail: string;
  tip: number;
  weight: number;
  workingProcessV2: WorkingProcessV2;
  isTet?: boolean;
  ecoOptions?: {
    status?: IStatusEcoOption;
  };
  monthlyOptions?: IMonthlyOption[];
  minTaskOfSubscription?: number;
  detailService?: {
    officeCleaning?: IDetailOfficeCleaning;
    massage?: IMassageDetailService;
    homeMoving?: IHomeMoving;
    housekeeping?: {
      city?: {
        name?: string;
        maxSupportDuration?: number;
        homeTypes?: IHostelType[];
      }[];
    };
  };
  tetBookingDates?: {
    bookTaskTime: {
      fromDate: number;
      toDate: number;
    };
  };
  limitBookTaskDates?: {
    city: string;
    limitBookTaskDate: number;
  }[];
}

export type IAddons = {
  name?: string;
  cost?: number;
  text?: IObjectText;
  // status?: StatusOption;
};
