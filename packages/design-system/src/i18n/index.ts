import { initReactI18next } from 'react-i18next';
import i18n from 'i18next';

import { translations } from './translations';

type SupportedLanguages = keyof typeof translations;

// Initialize with default language (will be updated by host app)
i18n.use(initReactI18next).init({
  compatibilityJSON: 'v4',
  resources: translations,
  lng: 'vi', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
  fallbackNS: 'common',
  defaultNS: 'common',
  simplifyPluralSuffix: false,
});

// Function to change language from host app
export const setLanguage = (language: string) => {
  if (Object.keys(translations).includes(language)) {
    i18n.changeLanguage(language as SupportedLanguages);
  }
};

export { i18n };
