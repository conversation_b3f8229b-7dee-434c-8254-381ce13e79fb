import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { apiRequest } from '../../api';
import { EndpointKeys } from '../../api/endpoints';
import { getISOCode } from '../../helpers';
import { IUser, Maybe } from '../../types';
import { IPaymentCard } from '../../types/card';
import { AppStorage } from '../../utils';

// Định nghĩa kiểu dữ liệu cho state
interface UserState {
  user?: Maybe<IUser>;
  setUser: (user: IUser) => void;
  getUser: () => Promise<Maybe<IUser>>;
  financialAccount: any;
  getFinancialAccount: () => void;
  cardPayment: Maybe<IPaymentCard>;
  getCardPayment: () => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: null,
      setUser: (user: IUser) => set({ user }),
      getUser: async () => {
        try {
          const response = await apiRequest({
            key: EndpointKeys.getUser,
            params: {},
          });
          set({ user: response as IUser });
          return response;
        } catch (error) {
          return null;
        }
      },
      financialAccount: null,
      getFinancialAccount: async () => {
        try {
          const response = await apiRequest({
            key: EndpointKeys.getFinancialAccount,
            params: {
              isoCode: getISOCode(),
            },
          });
          set({ financialAccount: response as any });
          return response;
        } catch (error) {
          return null;
        }
      },
      cardPayment: null,
      getCardPayment: async () => {
        try {
          const response = await apiRequest({
            key: EndpointKeys.getCardList,
            params: {
              isoCode: getISOCode(),
            },
          });
          set({ cardPayment: response as any });
          return response;
        } catch (error) {
          return null;
        }
      },
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
