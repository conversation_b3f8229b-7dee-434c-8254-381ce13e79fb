import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { i18n } from '../../i18n';
import { Maybe } from '../../types';
import { AppStorage } from '../../utils';
import { ISO_CODE } from '../../utils/constant';
import { LOCALES } from './../../utils/constant';

export type IFeatureConfig = {
  ENABLE_NEW_FEATURE_OPEN_MALAYSIA?: boolean;
  ENABLE_NEW_FEATURE_PUZZLE_GAME?: boolean;
  ENABLE_NEW_FEATURE_WATER_GUN?: boolean;
};

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho state
interface AppState {
  isFirstOpen?: boolean;
  isHadOpenExploreBtaskee?: boolean;
  isoCode?: Maybe<ISO_CODE>;
  locale: LOCALES;
  featureConfig?: IFeatureConfig;
  setFeatureConfig: (featureConfig?: IFeatureConfig) => void;
  onChangeIsoCode: (isoCode: ISO_CODE) => void;
  onChangeLocale: (locale: LOCALES) => void;
  onChangeIsFirstOpen: () => void;
  setIsHadOpenExploreBtaskee: (isHadOpenExploreBtaskee: boolean) => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      isFirstOpen: true,
      isHadOpenExploreBtaskee: false,
      isoCode: null,
      locale: LOCALES.vi,
      onChangeLocale: (locale: LOCALES) => {
        i18n.changeLanguage(locale);
        return set({ locale });
      },
      onChangeIsoCode: (isoCode) => set({ isoCode }),
      setFeatureConfig: (featureConfig) => set({ featureConfig }),
      onChangeIsFirstOpen: () => set({ isFirstOpen: false }),
      setIsHadOpenExploreBtaskee: (isHadOpenExploreBtaskee: boolean) =>
        set({ isHadOpenExploreBtaskee }),
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
