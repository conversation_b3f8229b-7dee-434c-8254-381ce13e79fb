import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { ISettings, Maybe } from '../../types';
import { AppStorage } from '../../utils';

// <PERSON><PERSON><PERSON> ngh<PERSON>a kiểu dữ liệu cho state
interface SettingsState {
  isLoading: number;
  settings?: Maybe<ISettings>;
  setSettings: (settings: ISettings) => void;
  showLoading: () => void;
  hideLoading: () => void;
  updateFavoriteService: (favoriteService: any) => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      isLoading: 0,
      settings: null,
      updateFavoriteService: (favoriteService: any) =>
        set((state) => ({
          settings: {
            ...state.settings,
            favouriteServices: favoriteService,
          },
        })),
      setSettings: (settings) => set({ settings }),
      showLoading: () => set((state) => ({ isLoading: state.isLoading + 1 })),
      hideLoading: () =>
        set((state) => ({
          isLoading: state.isLoading > 0 ? state.isLoading - 1 : 0,
        })),
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
