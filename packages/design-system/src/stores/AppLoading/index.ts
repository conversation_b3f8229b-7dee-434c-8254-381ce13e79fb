import { create } from 'zustand';

interface AppLoadingState {
  isLoading: boolean;
  showLoading: () => void;
  hideLoading: () => void;
}

export const useAppLoadingStore = create<AppLoadingState>()((set, get) => ({
  isLoading: false,
  showLoading: () => {
    const { isLoading } = get();
    if (!isLoading) {
      set(() => ({ isLoading: true }));
    }
  },
  hideLoading: () => {
    set(() => ({ isLoading: false }));
  },
}));
