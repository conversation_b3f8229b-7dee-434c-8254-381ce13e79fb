import { useCallback } from 'react';
import { File, Options, RNS3 } from 'react-native-aws3';
import get from 'lodash-es/get';

import {
  Alert,
  genUniqueString,
  getIsoCodeGlobal,
  ImageHelpers,
} from '../helpers';
import { useAppLoadingStore, useAppStore, useUserStore } from '../stores';
import type { IGallery, IImage } from '../types';
import { AppConfig } from '../utils';
import { STATUS_CODE_201 } from '../utils/constant';
import { useI18n } from './useI18n';

/**
 * Hook for handling image uploads across all microservices
 * Purpose: Provides centralized image upload functionality with proper error handling and loading states
 * @returns {object} Object containing image upload functions
 */
export const useUploadImage = () => {
  const { t } = useI18n('common');
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { showLoading, hideLoading } = useAppLoadingStore();

  /**
   * Processes a single image for upload
   * Purpose: Handles individual image processing and upload preparation
   * @param image - Image object containing URI and metadata
   * @param endPath - Optional path suffix for upload destination
   * @param keyPrefix - Optional prefix for upload key
   * @returns {Promise<IImage | undefined>} Processed image object with link
   */
  const getLinkImage = useCallback(
    async ({
      image,
      endPath,
      keyPrefix,
    }: {
      image: IImage;
      endPath?: string;
      keyPrefix?: string;
    }): Promise<IImage | undefined> => {
      if (ImageHelpers.isImageLocal(image.uri)) {
        const configLocal = await AppConfig.getFromStorage();
        const AWS3 = configLocal?.AWS3;
        try {
          const optionsAWS3 = {
            keyPrefix:
              keyPrefix ||
              `${AWS3?.keyPrefix}/uploadImage/${
                isoCode || getIsoCodeGlobal()
              }/${user?._id}/${endPath}/`,
            bucket: AWS3?.bucket,
            region: AWS3?.region,
            accessKey: AWS3?.accessKey,
            secretKey: AWS3?.secretKey,
            successActionStatus: 201,
          };

          const uniqueString = genUniqueString();
          const newImage: IImage = {
            ...image,
            _id: uniqueString,
            oldName: image.name,
            type: image.type || 'image/png',
            name: `${image.name}-${uniqueString}`,
          };
          const response = await RNS3.put(newImage, optionsAWS3);
          if (response?.status === STATUS_CODE_201) {
            const link = get(response, 'body.postResponse.location', null);
            return { ...newImage, link };
          }
        } catch (error) {
          Alert.alert.open?.({
            title: t('DIALOG_TITLE_INFORMATION'),
            message: t('UPLOAD_IMAGES_FAIL'),
            actions: [{ text: t('CLOSE') }],
          });
        }
      } else {
        return {
          ...image,
          oldName: image.name,
          link: image.uri,
        };
      }
    },
    [isoCode, t, user?._id],
  );

  /**
   * Processes multiple images for upload
   * Purpose: Handles batch image processing and upload preparation
   * @param images - Array of image objects to process
   * @param endPath - Optional path suffix for upload destination
   * @param keyPrefix - Optional prefix for upload key
   * @returns {Promise<IImage[]>} Array of processed image objects with links
   */
  const getMultipleLinkImages = async ({
    images,
    endPath,
    keyPrefix,
  }: {
    images: IImage[];
    endPath?: string;
    keyPrefix?: string;
  }): Promise<IImage[]> => {
    try {
      showLoading();

      const imageResponses = await Promise.all(
        images.map(async (image) => {
          return await getLinkImage({ image, endPath, keyPrefix });
        }),
      );

      const validImages = imageResponses.filter(
        (image): image is IImage => !!image,
      );

      hideLoading();
      return validImages;
    } catch (error) {
      hideLoading();
      Alert.alert.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('UPLOAD_IMAGES_FAIL'),
        actions: [{ text: t('CLOSE') }],
      });
      return [];
    }
  };

  /**
   * Uploads a single gallery item (image or video)
   * Purpose: Handles individual gallery item upload to AWS S3
   * @param gallery - Gallery object containing URI and metadata
   * @param endPath - Optional path suffix for upload destination
   * @param onError - Optional error callback function
   * @returns {Promise<IGallery | undefined>} Processed gallery object with link
   */
  const onUploadGallery = useCallback(
    async ({
      gallery,
      endPath,
      onError,
    }: {
      gallery?: IGallery;
      endPath?: string;
      onError?: () => void;
    }): Promise<IGallery | undefined> => {
      const configLocal = await AppConfig.getFromStorage();
      const AWS3 = configLocal?.AWS3;
      if (!AWS3) return;
      const { keyPrefix, bucket, region, accessKey, secretKey } = AWS3;
      if (!bucket || !region || !accessKey || !secretKey || !gallery) return;

      if (ImageHelpers.isImageLocal(gallery.uri)) {
        try {
          const currentIsoCode = isoCode || getIsoCodeGlobal();
          let newKeyPrefix = `${keyPrefix}upload/${currentIsoCode}/${user?._id}/`;
          if (endPath) {
            newKeyPrefix = `${keyPrefix}${endPath}/`;
          }
          const optionsAWS3: Options = {
            keyPrefix: newKeyPrefix,
            bucket,
            region: region,
            accessKey: accessKey,
            secretKey: secretKey,
            successActionStatus: 201,
          };

          const uniqueString = genUniqueString();
          const newImage: File = {
            uri: gallery.uri,
            type: gallery.type,
            name: `${uniqueString}-${gallery.name}`,
          };
          const response = await RNS3.put(newImage, optionsAWS3);
          if (response?.status === STATUS_CODE_201) {
            const link = get(response, 'body.postResponse.location', null);
            return {
              ...gallery,
              link,
            };
          }
        } catch (error) {
          onError
            ? onError()
            : Alert.alert.open?.({
                title: t('DIALOG_TITLE_INFORMATION'),
                message: t('UPLOAD_FAIL'),
                actions: [{ text: t('CLOSE') }],
              });

          return;
        }
      } else {
        return { ...gallery, link: gallery.uri };
      }
    },
    [isoCode, t, user?._id],
  );

  /**
   * Uploads multiple gallery items
   * Purpose: Handles batch gallery upload with error reporting
   * @param listGallery - Array of gallery objects to upload
   * @param endPath - Optional path suffix for upload destination
   * @returns {Promise<IGallery[]>} Array of processed gallery objects
   */
  const uploadMultiGallery = async ({
    listGallery,
    endPath,
  }: {
    listGallery?: IGallery[];
    endPath?: string;
  }): Promise<IGallery[]> => {
    if (!listGallery?.length) return [];

    const responses = await Promise.all(
      listGallery.map(async (gallery) => {
        return await onUploadGallery({ gallery, endPath, onError: () => {} });
      }),
    );

    const listError = responses.filter((e) => !e);
    if (listError.length) {
      Alert.alert.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('NUMBER_OF_UPLOAD_FAIL', {
          count: listError.length,
        }),
        actions: [{ text: t('CLOSE') }],
      });
    }

    return responses.filter((e) => !!e);
  };

  /**
   * Shows error toast for maximum images reached
   * Purpose: Displays user-friendly error message when image limit is exceeded
   * @returns {void} No return value, shows toast notification
   */
  const showToastMaxImages = (): void => {
    Alert.alert.open?.({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('MAXIMUM_IMAGES_REACHED'),
      actions: [{ text: t('CLOSE') }],
    });
  };

  return {
    getLinkImage,
    getMultipleLinkImages,
    onUploadGallery,
    uploadMultiGallery,
    showToastMaxImages,
    isImageLocal: ImageHelpers.isImageLocal,
  };
};
