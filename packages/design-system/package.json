{"name": "@btaskee/design-system", "version": "0.0.11", "main": "index.js", "types": "index.d.ts", "publishConfig": {"registry": "https://gitlab.com/api/v4/projects/69249955/packages/npm/"}, "exports": {".": {"default": "./index.ts", "types": "./index.d.ts"}}, "files": ["src", "index.ts", "index.d.ts", "patches"], "scripts": {"deploy": "npm publish", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepare": "npx husky install"}, "peerDependencies": {"@react-native-community/netinfo": "*", "@tanstack/react-query": "*", "dayjs": "*", "i18next": "*", "lodash": "*", "react": "*", "react-i18next": "*", "react-native": "*", "react-native-device-info": "*", "react-native-fast-image": "*", "react-native-image-crop-picker": "*", "react-native-markdown-display": "*", "react-native-mmkv": "*", "react-native-modal": "*", "react-native-permissions": "*", "react-native-safe-area-context": "*", "react-native-keychain": "*"}, "dependencies": {"@btaskee/auth-store": "0.0.1", "@gorhom/bottom-sheet": "5.1.6", "@react-native-community/netinfo": "11.4.1", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.2.0", "@shopify/flash-list": "1.8.2", "@tanstack/query-core": "5.79.0", "@tanstack/react-query": "5.79.0", "axios": "1.8.4", "crypto-js": "4.2.0", "dayjs": "1.9.6", "i18next": "25.1.3", "lodash": "4.17.16", "lodash-es": "4.17.21", "lottie-react-native": "7.2.2", "react": "18.3.1", "react-i18next": "15.5.1", "react-native": "0.77.2", "react-native-aws3": "^0.0.9", "react-native-calendars": "1.1312.0", "react-native-device-info": "14.0.4", "react-native-fast-image": "8.6.3", "react-native-gesture-handler": "2.25.0", "react-native-keyboard-controller": "1.17.5", "react-native-keychain": "10.0.0", "react-native-markdown-display": "7.0.2", "react-native-mmkv": "3.2.0", "react-native-modal": "14.0.0-rc.1", "react-native-permissions": "5.4.0", "react-native-reanimated": "3.17.5", "react-native-safe-area-context": "5.4.0", "vietnamese-lunar-calendar": "0.0.6", "zustand": "5.0.3"}, "devDependencies": {"@btaskee/config": "0.1.28", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@react-native/babel-preset": "0.77.2", "@react-native/eslint-config": "0.77.2", "@react-native/eslint-plugin": "0.77.2", "@react-native/typescript-config": "0.79.2", "@types/crypto-js": "4.2.2", "@types/jest": "29.5.13", "@types/lodash": "4.17.17", "@types/lodash-es": "4.17.12", "@types/react": "18.2.6", "@types/react-native": "0.73.0", "@typescript-eslint/eslint-plugin": "7.1.1", "@typescript-eslint/parser": "7.1.1", "eslint": "8.43.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-extra-rules": "0.0.0-development", "eslint-plugin-ft-flow": "2.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "27.9.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.30.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-native": "4.0.0", "eslint-plugin-simple-import-sort": "12.1.1", "husky": "8.0.3", "jest": "29.6.3", "prettier": "2.8.8"}}